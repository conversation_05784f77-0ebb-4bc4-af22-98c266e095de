{"name": "@vitest/expect", "type": "module", "version": "4.0.12", "description": "Jest's expect matchers as a Chai plugin", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/expect"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {"@standard-schema/spec": "^1.0.0", "@types/chai": "^5.2.2", "chai": "^6.2.1", "tinyrainbow": "^3.0.3", "@vitest/spy": "4.0.12", "@vitest/utils": "4.0.12"}, "devDependencies": {"@vitest/runner": "4.0.12"}, "scripts": {"build": "premove dist && rollup -c", "dev": "rollup -c --watch"}}