{"name": "@schematics/angular", "version": "20.3.11", "description": "Schematics specific to Angular", "homepage": "https://github.com/angular/angular-cli", "keywords": ["Angular CLI", "Angular DevKit", "angular", "blueprints", "code generation", "devkit", "schematics", "sdk"], "exports": {"./package.json": "./package.json", "./utility": "./utility/index.js", "./utility/*": "./utility/*.js", "./migrations/migration-collection.json": "./migrations/migration-collection.json", "./*": "./*.js"}, "schematics": "./collection.json", "dependencies": {"@angular-devkit/core": "20.3.11", "@angular-devkit/schematics": "20.3.11", "jsonc-parser": "3.3.1"}, "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "packageManager": "pnpm@10.19.0", "engines": {"node": "^20.19.0 || ^22.12.0 || >=24.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}}