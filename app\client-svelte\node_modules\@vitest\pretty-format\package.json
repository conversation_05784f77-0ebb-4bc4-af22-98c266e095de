{"name": "@vitest/pretty-format", "type": "module", "version": "4.0.12", "description": "Fork of pretty-format with support for ESM", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/pretty-format"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["*.d.ts", "dist"], "dependencies": {"tinyrainbow": "^3.0.3"}, "devDependencies": {"@types/react-is": "^19.2.0", "react-is": "^19.2.0", "react-is-18": "npm:react-is@18.3.1"}, "scripts": {"build": "premove dist && rollup -c", "dev": "rollup -c --watch"}}