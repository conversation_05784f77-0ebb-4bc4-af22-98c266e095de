export { B as BaseCoverageProvider } from './chunks/coverage.CtyeYmKM.js';
import 'node:fs';
import 'node:path';
import '@vitest/utils/helpers';
import 'pathe';
import 'picomatch';
import 'tinyglobby';
import 'tinyrainbow';
import './chunks/defaults.BOqNVLsY.js';
import 'node:os';
import './chunks/env.D4Lgay0q.js';
import 'std-env';
import 'node:crypto';
import 'node:url';
import 'node:module';
import 'node:process';
import 'node:fs/promises';
import 'node:assert';
import 'node:v8';
import 'node:util';
import 'vite';
import './chunks/constants.D_Q9UYh-.js';
import './chunks/coverage.D_JHT54q.js';
