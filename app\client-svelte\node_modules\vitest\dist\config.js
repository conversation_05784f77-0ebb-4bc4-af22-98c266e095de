export { c as configDefaults, a as coverageConfigDefaults, d as defaultExclude, b as defaultInclude } from './chunks/defaults.BOqNVLsY.js';
export { mergeConfig } from 'vite';
export { d as defaultBrowserPort } from './chunks/constants.D_Q9UYh-.js';
import 'node:os';
import './chunks/env.D4Lgay0q.js';
import 'std-env';

function defineConfig(config) {
	return config;
}
function defineProject(config) {
	return config;
}

export { defineConfig, defineProject };
