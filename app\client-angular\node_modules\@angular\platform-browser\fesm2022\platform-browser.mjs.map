{"version": 3, "file": "platform-browser.mjs", "sources": ["../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/platform-browser/src/browser/meta.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/platform-browser/src/browser/title.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/platform-browser/src/dom/util.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/platform-browser/src/browser/tools/common_tools.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/platform-browser/src/browser/tools/tools.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/platform-browser/src/dom/debug/by.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/platform-browser/src/dom/events/hammer_gestures.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/platform-browser/src/security/dom_sanitization_service.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/platform-browser/src/hydration.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/platform-browser/src/version.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT, ɵDomAdapter as DomAdapter, ɵgetDOM as getDOM} from '@angular/common';\nimport {Inject, Injectable} from '@angular/core';\n\n/**\n * Represents the attributes of an HTML `<meta>` element. The element itself is\n * represented by the internal `HTMLMetaElement`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see {@link Meta}\n *\n * @publicApi\n */\nexport type MetaDefinition = {\n  charset?: string;\n  content?: string;\n  httpEquiv?: string;\n  id?: string;\n  itemprop?: string;\n  name?: string;\n  property?: string;\n  scheme?: string;\n  url?: string;\n} & {\n  // TODO(IgorMinar): this type looks wrong\n  [prop: string]: string;\n};\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root'})\nexport class Meta {\n  private _dom: DomAdapter;\n  constructor(@Inject(DOCUMENT) private _doc: any) {\n    this._dom = getDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag: MetaDefinition, forceCreation: boolean = false): HTMLMetaElement | null {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags: MetaDefinition[], forceCreation: boolean = false): HTMLMetaElement[] {\n    if (!tags) return [];\n    return tags.reduce((result: HTMLMetaElement[], tag: MetaDefinition) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector: string): HTMLMetaElement | null {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector: string): HTMLMetaElement[] {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag: MetaDefinition, selector?: string): HTMLMetaElement | null {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta: HTMLMetaElement = this.getTag(selector)!;\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector: string): void {\n    this.removeTagElement(this.getTag(attrSelector)!);\n  }\n\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta: HTMLMetaElement): void {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n\n  private _getOrCreateElement(\n    meta: MetaDefinition,\n    forceCreation: boolean = false,\n  ): HTMLMetaElement {\n    if (!forceCreation) {\n      const selector: string = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter((elem) => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element: HTMLMetaElement = this._dom.createElement('meta') as HTMLMetaElement;\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n\n  private _setMetaElementAttributes(tag: MetaDefinition, el: HTMLMetaElement): HTMLMetaElement {\n    Object.keys(tag).forEach((prop: string) =>\n      el.setAttribute(this._getMetaKeyMap(prop), tag[prop]),\n    );\n    return el;\n  }\n\n  private _parseSelector(tag: MetaDefinition): string {\n    const attr: string = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n\n  private _containsAttributes(tag: MetaDefinition, elem: HTMLMetaElement): boolean {\n    return Object.keys(tag).every(\n      (key: string) => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key],\n    );\n  }\n\n  private _getMetaKeyMap(prop: string): string {\n    return META_KEYS_MAP[prop] || prop;\n  }\n}\n\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP: {[prop: string]: string} = {\n  httpEquiv: 'http-equiv',\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {Inject, Injectable} from '@angular/core';\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root'})\nexport class Title {\n  constructor(@Inject(DOCUMENT) private _doc: any) {}\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle(): string {\n    return this._doc.title;\n  }\n\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle: string) {\n    this._doc.title = newTitle || '';\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/// <reference path=\"../../../goog.d.ts\" />\n\nimport {ɵglobal as global} from '@angular/core';\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nexport function exportNgVar(name: string, value: any): void {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = (global['ng'] = (global['ng'] as {[key: string]: any} | undefined) || {});\n    ng[name] = value;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ApplicationRef, ComponentRef} from '@angular/core';\n\nexport class ChangeDetectionPerfRecord {\n  constructor(\n    public msPerTick: number,\n    public numTicks: number,\n  ) {}\n}\n\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nexport class AngularProfiler {\n  appRef: ApplicationRef;\n\n  constructor(ref: ComponentRef<any>) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```ts\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config: any): ChangeDetectionPerfRecord {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    if (record && 'profile' in console && typeof console.profile === 'function') {\n      console.profile(profileName);\n    }\n    const start = performance.now();\n    let numTicks = 0;\n    while (numTicks < 5 || performance.now() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performance.now();\n    if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n      console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    console.log(`ran ${numTicks} change detection cycles`);\n    console.log(`${msPerTick.toFixed(2)} ms per check`);\n\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentRef} from '@angular/core';\n\nimport {exportNgVar} from '../../dom/util';\n\nimport {AngularProfiler} from './common_tools';\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nexport function enableDebugTools<T>(ref: ComponentRef<T>): ComponentRef<T> {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nexport function disableDebugTools(): void {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ɵgetDOM as getDOM} from '@angular/common';\nimport {DebugElement, DebugNode, Predicate, Type} from '@angular/core';\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nexport class By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all(): Predicate<DebugNode> {\n    return () => true;\n  }\n\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector: string): Predicate<DebugElement> {\n    return (debugElement) => {\n      return debugElement.nativeElement != null\n        ? elementMatches(debugElement.nativeElement, selector)\n        : false;\n    };\n  }\n\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type: Type<any>): Predicate<DebugNode> {\n    return (debugNode) => debugNode.providerTokens!.indexOf(type) !== -1;\n  }\n}\n\nfunction elementMatches(n: any, selector: string): boolean {\n  if (getDOM().isElementNode(n)) {\n    return (\n      (n.matches && n.matches(selector)) ||\n      (n.msMatchesSelector && n.msMatchesSelector(selector)) ||\n      (n.webkitMatchesSelector && n.webkitMatchesSelector(selector))\n    );\n  }\n\n  return false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/// <reference types=\"hammerjs\" />\n\nimport {DOCUMENT} from '@angular/common';\nimport {\n  Inject,\n  Injectable,\n  InjectionToken,\n  Injector,\n  NgModule,\n  Optional,\n  ɵConsole as Console,\n} from '@angular/core';\n\nimport {EVENT_MANAGER_PLUGINS} from './event_manager';\nimport {EventManagerPlugin} from './event_manager_plugin';\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true,\n};\n\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nexport const HAMMER_GESTURE_CONFIG = new InjectionToken<HammerGestureConfig>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerGestureConfig' : '',\n);\n\n/**\n * Function that loads HammerJS, returning a promise that is resolved once HammerJs is loaded.\n *\n * @publicApi\n *\n * @deprecated The hammerjs integration is deprecated. Replace it by your own implementation.\n */\nexport type HammerLoader = () => Promise<void>;\n\n/**\n * Injection token used to provide a HammerLoader to Angular.\n *\n * @see {@link HammerLoader}\n *\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nexport const HAMMER_LOADER = new InjectionToken<HammerLoader>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerLoader' : '',\n);\n\nexport interface HammerInstance {\n  on(eventName: string, callback?: Function): void;\n  off(eventName: string, callback?: Function): void;\n  destroy?(): void;\n}\n\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\n@Injectable()\nexport class HammerGestureConfig {\n  /**\n   * A set of supported event names for gestures to be used in Angular.\n   * Angular supports all built-in recognizers, as listed in\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  events: string[] = [];\n\n  /**\n   * Maps gesture event names to a set of configuration options\n   * that specify overrides to the default values for specific properties.\n   *\n   * The key is a supported event name to be configured,\n   * and the options object contains a set of properties, with override values\n   * to be applied to the named recognizer event.\n   * For example, to disable recognition of the rotate event, specify\n   *  `{\"rotate\": {\"enable\": false}}`.\n   *\n   * Properties that are not present take the HammerJS default values.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   *\n   */\n  overrides: {[key: string]: Object} = {};\n\n  /**\n   * Properties whose default values can be overridden for a given event.\n   * Different sets of properties apply to different events.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  options?: {\n    cssProps?: any;\n    domEvents?: boolean;\n    enable?: boolean | ((manager: any) => boolean);\n    preset?: any[];\n    touchAction?: string;\n    recognizers?: any[];\n    inputClass?: any;\n    inputTarget?: EventTarget;\n  };\n\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element: HTMLElement): HammerInstance {\n    const mc = new Hammer!(element, this.options);\n\n    mc.get('pinch').set({enable: true});\n    mc.get('rotate').set({enable: true});\n\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n\n    return mc;\n  }\n}\n\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\n@Injectable()\nexport class HammerGesturesPlugin extends EventManagerPlugin {\n  private _loaderPromise: Promise<void> | null = null;\n\n  constructor(\n    @Inject(DOCUMENT) doc: any,\n    @Inject(HAMMER_GESTURE_CONFIG) private _config: HammerGestureConfig,\n    private _injector: Injector,\n    @Optional() @Inject(HAMMER_LOADER) private loader?: HammerLoader | null,\n  ) {\n    super(doc);\n  }\n\n  override supports(eventName: string): boolean {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n\n    if (!(window as any).Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Get a `Console` through an injector to tree-shake the\n        // class when it is unused in production.\n        const _console = this._injector.get(Console);\n        _console.warn(\n          `The \"${eventName}\" event cannot be bound because Hammer.JS is not ` +\n            `loaded and no custom loader has been specified.`,\n        );\n      }\n      return false;\n    }\n\n    return true;\n  }\n\n  override addEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!(window as any).Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader!());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister: Function = () => {\n        cancelRegistration = true;\n      };\n\n      zone.runOutsideAngular(() =>\n        this._loaderPromise!.then(() => {\n          // If Hammer isn't actually loaded when the custom loader resolves, give up.\n          if (!(window as any).Hammer) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n              const _console = this._injector.get(Console);\n              _console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n            }\n            deregister = () => {};\n            return;\n          }\n\n          if (!cancelRegistration) {\n            // Now that Hammer is loaded and the listener is being loaded for real,\n            // the deregistration function changes from canceling registration to\n            // removal.\n            deregister = this.addEventListener(element, eventName, handler);\n          }\n        }).catch(() => {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const _console = this._injector.get(Console);\n            _console.warn(\n              `The \"${eventName}\" event cannot be bound because the custom ` +\n                `Hammer.JS loader failed.`,\n            );\n          }\n          deregister = () => {};\n        }),\n      );\n\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function (eventObj: HammerInput) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n\n  isCustomEvent(eventName: string): boolean {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n}\n\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n *\n * @deprecated The hammer integration is deprecated. Replace it by your own implementation.\n */\n@NgModule({\n  providers: [\n    {\n      provide: EVENT_MANAGER_PLUGINS,\n      useClass: HammerGesturesPlugin,\n      multi: true,\n      deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]],\n    },\n    {provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig},\n  ],\n})\nexport class HammerModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {\n  forwardRef,\n  Inject,\n  Injectable,\n  Sanitizer,\n  SecurityContext,\n  ɵ_sanitizeHtml as _sanitizeHtml,\n  ɵ_sanitizeUrl as _sanitizeUrl,\n  ɵallowSanitizationBypassAndThrow as allowSanitizationBypassOrThrow,\n  ɵbypassSanitizationTrustHtml as bypassSanitizationTrustHtml,\n  ɵbypassSanitizationTrustResourceUrl as bypassSanitizationTrustResourceUrl,\n  ɵbypassSanitizationTrustScript as bypassSanitizationTrustScript,\n  ɵbypassSanitizationTrustStyle as bypassSanitizationTrustStyle,\n  ɵbypassSanitizationTrustUrl as bypassSanitizationTrustUrl,\n  ɵBypassType as BypassType,\n  ɵRuntimeError as RuntimeError,\n  ɵunwrapSafeValue as unwrapSafeValue,\n  ɵXSS_SECURITY_URL as XSS_SECURITY_URL,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nexport {SecurityContext};\n\n/**\n * Marker interface for a value that's safe to use in a particular context.\n *\n * @publicApi\n */\nexport interface SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as HTML.\n *\n * @publicApi\n */\nexport interface SafeHtml extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as style (CSS).\n *\n * @publicApi\n */\nexport interface SafeStyle extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as JavaScript.\n *\n * @publicApi\n */\nexport interface SafeScript extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as a URL linking to a document.\n *\n * @publicApi\n */\nexport interface SafeUrl extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as a URL to load executable code from.\n *\n * @publicApi\n */\nexport interface SafeResourceUrl extends SafeValue {}\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root', useExisting: forwardRef(() => DomSanitizerImpl)})\nexport abstract class DomSanitizer implements Sanitizer {\n  /**\n   * Gets a safe value from either a known safe value or a value with unknown safety.\n   *\n   * If the given value is already a `SafeValue`, this method returns the unwrapped value.\n   * If the security context is HTML and the given value is a plain string, this method\n   * sanitizes the string, removing any potentially unsafe content.\n   * For any other security context, this method throws an error if provided\n   * with a plain string.\n   */\n  abstract sanitize(context: SecurityContext, value: SafeValue | string | null): string | null;\n\n  /**\n   * Bypass security and trust the given value to be safe HTML. Only use this when the bound HTML\n   * is unsafe (e.g. contains `<script>` tags) and the code should be executed. The sanitizer will\n   * leave safe HTML intact, so in most situations this method should not be used.\n   *\n   * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n   * security risks!\n   */\n  abstract bypassSecurityTrustHtml(value: string): SafeHtml;\n\n  /**\n   * Bypass security and trust the given value to be safe style value (CSS).\n   *\n   * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n   * security risks!\n   */\n  abstract bypassSecurityTrustStyle(value: string): SafeStyle;\n\n  /**\n   * Bypass security and trust the given value to be safe JavaScript.\n   *\n   * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n   * security risks!\n   */\n  abstract bypassSecurityTrustScript(value: string): SafeScript;\n\n  /**\n   * Bypass security and trust the given value to be a safe style URL, i.e. a value that can be used\n   * in hyperlinks or `<img src>`.\n   *\n   * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n   * security risks!\n   */\n  abstract bypassSecurityTrustUrl(value: string): SafeUrl;\n\n  /**\n   * Bypass security and trust the given value to be a safe resource URL, i.e. a location that may\n   * be used to load executable code from, like `<script src>`, or `<iframe src>`.\n   *\n   * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n   * security risks!\n   */\n  abstract bypassSecurityTrustResourceUrl(value: string): SafeResourceUrl;\n}\n\n@Injectable({providedIn: 'root'})\nexport class DomSanitizerImpl extends DomSanitizer {\n  constructor(@Inject(DOCUMENT) private _doc: any) {\n    super();\n  }\n\n  override sanitize(ctx: SecurityContext, value: SafeValue | string | null): string | null {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value as string;\n      case SecurityContext.HTML:\n        if (allowSanitizationBypassOrThrow(value, BypassType.Html)) {\n          return unwrapSafeValue(value);\n        }\n        return _sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (allowSanitizationBypassOrThrow(value, BypassType.Style)) {\n          return unwrapSafeValue(value);\n        }\n        return value as string;\n      case SecurityContext.SCRIPT:\n        if (allowSanitizationBypassOrThrow(value, BypassType.Script)) {\n          return unwrapSafeValue(value);\n        }\n        throw new RuntimeError(\n          RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT,\n          (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            'unsafe value used in a script context',\n        );\n      case SecurityContext.URL:\n        if (allowSanitizationBypassOrThrow(value, BypassType.Url)) {\n          return unwrapSafeValue(value);\n        }\n        return _sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (allowSanitizationBypassOrThrow(value, BypassType.ResourceUrl)) {\n          return unwrapSafeValue(value);\n        }\n        throw new RuntimeError(\n          RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL,\n          (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            `unsafe value used in a resource URL context (see ${XSS_SECURITY_URL})`,\n        );\n      default:\n        throw new RuntimeError(\n          RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX,\n          (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            `Unexpected SecurityContext ${ctx} (see ${XSS_SECURITY_URL})`,\n        );\n    }\n  }\n\n  override bypassSecurityTrustHtml(value: string): SafeHtml {\n    return bypassSanitizationTrustHtml(value);\n  }\n  override bypassSecurityTrustStyle(value: string): SafeStyle {\n    return bypassSanitizationTrustStyle(value);\n  }\n  override bypassSecurityTrustScript(value: string): SafeScript {\n    return bypassSanitizationTrustScript(value);\n  }\n  override bypassSecurityTrustUrl(value: string): SafeUrl {\n    return bypassSanitizationTrustUrl(value);\n  }\n  override bypassSecurityTrustResourceUrl(value: string): SafeResourceUrl {\n    return bypassSanitizationTrustResourceUrl(value);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HttpTransferCacheOptions, ɵwithHttpTransferCache} from '@angular/common/http';\nimport {\n  ENVIRONMENT_INITIALIZER,\n  EnvironmentProviders,\n  inject,\n  makeEnvironmentProviders,\n  NgZone,\n  Provider,\n  ɵConsole as Console,\n  ɵRuntimeError as RuntimeError,\n  ɵformatRuntimeError as formatRuntimeError,\n  ɵwithDomHydration as withDomHydration,\n  ɵwithEventReplay,\n  ɵwithI18nSupport,\n  ɵZONELESS_ENABLED as ZONELESS_ENABLED,\n  ɵwithIncrementalHydration,\n  ɵIS_ENABLED_BLOCKING_INITIAL_NAVIGATION as IS_ENABLED_BLOCKING_INITIAL_NAVIGATION,\n} from '@angular/core';\nimport {RuntimeErrorCode} from './errors';\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nexport enum HydrationFeatureKind {\n  NoHttpTransferCache,\n  HttpTransferCacheOptions,\n  I18nSupport,\n  EventReplay,\n  IncrementalHydration,\n}\n\n/**\n * Helper type to represent a Hydration feature.\n *\n * @publicApi\n */\nexport interface HydrationFeature<FeatureKind extends HydrationFeatureKind> {\n  ɵkind: FeatureKind;\n  ɵproviders: Provider[];\n}\n\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature<FeatureKind extends HydrationFeatureKind>(\n  ɵkind: FeatureKind,\n  ɵproviders: Provider[] = [],\n  ɵoptions: unknown = {},\n): HydrationFeature<FeatureKind> {\n  return {ɵkind, ɵproviders};\n}\n\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @see [Disabling Caching](guide/ssr#disabling-caching)\n *\n * @publicApi\n */\nexport function withNoHttpTransferCache(): HydrationFeature<HydrationFeatureKind.NoHttpTransferCache> {\n  // This feature has no providers and acts as a flag that turns off\n  // HTTP transfer cache (which otherwise is turned on by default).\n  return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n\n/**\n * The function accepts an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * whether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @see [Configuring HTTP transfer cache options](guide/ssr#caching-data-when-using-httpclient)\n *\n * @publicApi\n */\nexport function withHttpTransferCacheOptions(\n  options: HttpTransferCacheOptions,\n): HydrationFeature<HydrationFeatureKind.HttpTransferCacheOptions> {\n  // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n  return hydrationFeature(\n    HydrationFeatureKind.HttpTransferCacheOptions,\n    ɵwithHttpTransferCache(options),\n  );\n}\n\n/**\n * Enables support for hydrating i18n blocks.\n *\n * @publicApi 20.0\n */\nexport function withI18nSupport(): HydrationFeature<HydrationFeatureKind.I18nSupport> {\n  return hydrationFeature(HydrationFeatureKind.I18nSupport, ɵwithI18nSupport());\n}\n\n/**\n * Enables support for replaying user events (e.g. `click`s) that happened on a page\n * before hydration logic has completed. Once an application is hydrated, all captured\n * events are replayed and relevant event listeners are executed.\n *\n * @usageNotes\n *\n * Basic example of how you can enable event replay in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withEventReplay())]\n * });\n * ```\n * @publicApi\n * @see {@link provideClientHydration}\n */\nexport function withEventReplay(): HydrationFeature<HydrationFeatureKind.EventReplay> {\n  return hydrationFeature(HydrationFeatureKind.EventReplay, ɵwithEventReplay());\n}\n\n/**\n * Enables support for incremental hydration using the `hydrate` trigger syntax.\n *\n * @usageNotes\n *\n * Basic example of how you can enable incremental hydration in your application when\n * the `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withIncrementalHydration())]\n * });\n * ```\n * @publicApi 20.0\n * @see {@link provideClientHydration}\n */\nexport function withIncrementalHydration(): HydrationFeature<HydrationFeatureKind.IncrementalHydration> {\n  return hydrationFeature(HydrationFeatureKind.IncrementalHydration, ɵwithIncrementalHydration());\n}\n\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector(): Provider[] {\n  return [\n    {\n      provide: ENVIRONMENT_INITIALIZER,\n      useValue: () => {\n        const ngZone = inject(NgZone);\n        const isZoneless = inject(ZONELESS_ENABLED);\n        // Checking `ngZone instanceof NgZone` would be insufficient here,\n        // because custom implementations might use NgZone as a base class.\n        if (!isZoneless && ngZone.constructor !== NgZone) {\n          const console = inject(Console);\n          const message = formatRuntimeError(\n            RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE,\n            'Angular detected that hydration was enabled for an application ' +\n              'that uses a custom or a noop Zone.js implementation. ' +\n              'This is not yet a fully supported configuration.',\n          );\n          console.warn(message);\n        }\n      },\n      multi: true,\n    },\n  ];\n}\n\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether enabledBlocking initial navigation is used in an application\n * and logs a warning in a console if it's not compatible with hydration.\n */\nfunction provideEnabledBlockingInitialNavigationDetector(): Provider[] {\n  return [\n    {\n      provide: ENVIRONMENT_INITIALIZER,\n      useValue: () => {\n        const isEnabledBlockingInitialNavigation = inject(IS_ENABLED_BLOCKING_INITIAL_NAVIGATION, {\n          optional: true,\n        });\n\n        if (isEnabledBlockingInitialNavigation) {\n          const console = inject(Console);\n          const message = formatRuntimeError(\n            RuntimeErrorCode.HYDRATION_CONFLICTING_FEATURES,\n            'Configuration error: found both hydration and enabledBlocking initial navigation ' +\n              'in the same application, which is a contradiction.',\n          );\n          console.warn(message);\n        }\n      },\n      multi: true,\n    },\n  ];\n}\n\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or enable new ones:\n *\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n * * {@link withI18nSupport} to enable hydration support for i18n blocks\n * * {@link withEventReplay} to enable support for replaying user events\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```ts\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n * @see {@link withI18nSupport}\n * @see {@link withEventReplay}\n *\n * @param features Optional features to configure additional hydration behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi 17.0\n */\nexport function provideClientHydration(\n  ...features: HydrationFeature<HydrationFeatureKind>[]\n): EnvironmentProviders {\n  const providers: Provider[] = [];\n  const featuresKind = new Set<HydrationFeatureKind>();\n\n  for (const {ɵproviders, ɵkind} of features) {\n    featuresKind.add(ɵkind);\n\n    if (ɵproviders.length) {\n      providers.push(ɵproviders);\n    }\n  }\n\n  const hasHttpTransferCacheOptions = featuresKind.has(\n    HydrationFeatureKind.HttpTransferCacheOptions,\n  );\n\n  if (\n    typeof ngDevMode !== 'undefined' &&\n    ngDevMode &&\n    featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) &&\n    hasHttpTransferCacheOptions\n  ) {\n    throw new RuntimeError(\n      RuntimeErrorCode.HYDRATION_CONFLICTING_FEATURES,\n      'Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.',\n    );\n  }\n\n  return makeEnvironmentProviders([\n    typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [],\n    typeof ngDevMode !== 'undefined' && ngDevMode\n      ? provideEnabledBlockingInitialNavigationDetector()\n      : [],\n    withDomHydration(),\n    featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions\n      ? []\n      : ɵwithHttpTransferCache({}),\n    providers,\n  ]);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n\nimport {Version} from '@angular/core';\n\n/**\n * @publicApi\n */\nexport const VERSION = /* @__PURE__ */ new Version('20.3.13');\n"], "names": ["getDOM", "global", "<PERSON><PERSON><PERSON>", "allowSanitizationBypassOrThrow", "unwrapSafeValue", "_sanitizeHtml", "RuntimeError", "_sanitizeUrl", "XSS_SECURITY_URL", "bypassSanitizationTrustHtml", "bypassSanitizationTrustStyle", "bypassSanitizationTrustScript", "bypassSanitizationTrustUrl", "bypassSanitizationTrustResourceUrl", "ɵwithHttpTransferCache", "ɵwithI18nSupport", "ɵwithEventReplay", "ɵwithIncrementalHydration", "ZONELESS_ENABLED", "formatRuntimeError", "IS_ENABLED_BLOCKING_INITIAL_NAVIGATION", "withDomHydration"], "mappings": ";;;;;;;;;;;;;;;AAmCA;;;;;;;;;;;;;;;;;;;;;AAqBG;MAEU,IAAI,CAAA;AAEuB,IAAA,IAAA;AAD9B,IAAA,IAAI;AACZ,IAAA,WAAA,CAAsC,IAAS,EAAA;QAAT,IAAI,CAAA,IAAA,GAAJ,IAAI;AACxC,QAAA,IAAI,CAAC,IAAI,GAAGA,OAAM,EAAE;;AAEtB;;;;;;;;;AASG;AACH,IAAA,MAAM,CAAC,GAAmB,EAAE,aAAA,GAAyB,KAAK,EAAA;AACxD,QAAA,IAAI,CAAC,GAAG;AAAE,YAAA,OAAO,IAAI;QACrB,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,aAAa,CAAC;;AAGrD;;;;;;;AAOG;AACH,IAAA,OAAO,CAAC,IAAsB,EAAE,aAAA,GAAyB,KAAK,EAAA;AAC5D,QAAA,IAAI,CAAC,IAAI;AAAE,YAAA,OAAO,EAAE;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,MAAyB,EAAE,GAAmB,KAAI;YACpE,IAAI,GAAG,EAAE;AACP,gBAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;;AAE3D,YAAA,OAAO,MAAM;SACd,EAAE,EAAE,CAAC;;AAGR;;;;;AAKG;AACH,IAAA,MAAM,CAAC,YAAoB,EAAA;AACzB,QAAA,IAAI,CAAC,YAAY;AAAE,YAAA,OAAO,IAAI;AAC9B,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAQ,KAAA,EAAA,YAAY,CAAG,CAAA,CAAA,CAAC,IAAI,IAAI;;AAGjE;;;;;AAKG;AACH,IAAA,OAAO,CAAC,YAAoB,EAAA;AAC1B,QAAA,IAAI,CAAC,YAAY;AAAE,YAAA,OAAO,EAAE;AAC5B,QAAA,MAAM,IAAI,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA,KAAA,EAAQ,YAAY,CAAA,CAAA,CAAG,CAAC;AAC7E,QAAA,OAAO,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;;AAGxC;;;;;;;;AAQG;IACH,SAAS,CAAC,GAAmB,EAAE,QAAiB,EAAA;AAC9C,QAAA,IAAI,CAAC,GAAG;AAAE,YAAA,OAAO,IAAI;QACrB,QAAQ,GAAG,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAC/C,MAAM,IAAI,GAAoB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAE;QACpD,IAAI,IAAI,EAAE;YACR,OAAO,IAAI,CAAC,yBAAyB,CAAC,GAAG,EAAE,IAAI,CAAC;;QAElD,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,IAAI,CAAC;;AAG5C;;;;AAIG;AACH,IAAA,SAAS,CAAC,YAAoB,EAAA;QAC5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAE,CAAC;;AAGnD;;;AAGG;AACH,IAAA,gBAAgB,CAAC,IAAqB,EAAA;QACpC,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;;;AAIlB,IAAA,mBAAmB,CACzB,IAAoB,EACpB,aAAA,GAAyB,KAAK,EAAA;QAE9B,IAAI,CAAC,aAAa,EAAE;YAClB,MAAM,QAAQ,GAAW,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;;;;AAIlD,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7F,IAAI,IAAI,KAAK,SAAS;AAAE,gBAAA,OAAO,IAAI;;QAErC,MAAM,OAAO,GAAoB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAoB;AACnF,QAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC;AAC7C,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACzB,QAAA,OAAO,OAAO;;IAGR,yBAAyB,CAAC,GAAmB,EAAE,EAAmB,EAAA;AACxE,QAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAY,KACpC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CACtD;AACD,QAAA,OAAO,EAAE;;AAGH,IAAA,cAAc,CAAC,GAAmB,EAAA;AACxC,QAAA,MAAM,IAAI,GAAW,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG,UAAU;QACnD,OAAO,CAAA,EAAG,IAAI,CAAK,EAAA,EAAA,GAAG,CAAC,IAAI,CAAC,GAAG;;IAGzB,mBAAmB,CAAC,GAAmB,EAAE,IAAqB,EAAA;AACpE,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAC3B,CAAC,GAAW,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAC1E;;AAGK,IAAA,cAAc,CAAC,IAAY,EAAA;AACjC,QAAA,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI;;AAzIzB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAI,kBAEK,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAFjB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAI,cADQ,MAAM,EAAA,CAAA;;sGAClB,IAAI,EAAA,UAAA,EAAA,CAAA;kBADhB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;0BAGjB,MAAM;2BAAC,QAAQ;;AA2I9B;;AAEG;AACH,MAAM,aAAa,GAA6B;AAC9C,IAAA,SAAS,EAAE,YAAY;CACxB;;ACjMD;;;;;;;;;AASG;MAEU,KAAK,CAAA;AACsB,IAAA,IAAA;AAAtC,IAAA,WAAA,CAAsC,IAAS,EAAA;QAAT,IAAI,CAAA,IAAA,GAAJ,IAAI;;AAC1C;;AAEG;IACH,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;;AAGxB;;;AAGG;AACH,IAAA,QAAQ,CAAC,QAAgB,EAAA;QACvB,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,QAAQ,IAAI,EAAE;;AAdvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAK,kBACI,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AADjB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAK,cADO,MAAM,EAAA,CAAA;;sGAClB,KAAK,EAAA,UAAA,EAAA,CAAA;kBADjB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;0BAEjB,MAAM;2BAAC,QAAQ;;;ACf9B;AAIA;;;;;;AAMG;AACa,SAAA,WAAW,CAAC,IAAY,EAAE,KAAU,EAAA;IAClD,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,CAAC,QAAQ,EAAE;;;;;AAKhD,QAAA,MAAM,EAAE,IAAIC,OAAM,CAAC,IAAI,CAAC,GAAIA,OAAM,CAAC,IAAI,CAAsC,IAAI,EAAE,CAAC;AACpF,QAAA,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK;;AAEpB;;MClBa,yBAAyB,CAAA;AAE3B,IAAA,SAAA;AACA,IAAA,QAAA;IAFT,WACS,CAAA,SAAiB,EACjB,QAAgB,EAAA;QADhB,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAQ,CAAA,QAAA,GAAR,QAAQ;;AAElB;AAED;;;AAGG;MACU,eAAe,CAAA;AAC1B,IAAA,MAAM;AAEN,IAAA,WAAA,CAAY,GAAsB,EAAA;QAChC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC;;;AAIhD;;;;;;;;;;;;;;;AAeG;AACH,IAAA,mBAAmB,CAAC,MAAW,EAAA;QAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC;QACzC,MAAM,WAAW,GAAG,kBAAkB;;AAEtC,QAAA,IAAI,MAAM,IAAI,SAAS,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,UAAU,EAAE;AAC3E,YAAA,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;;AAE9B,QAAA,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE;QAC/B,IAAI,QAAQ,GAAG,CAAC;AAChB,QAAA,OAAO,QAAQ,GAAG,CAAC,IAAI,WAAW,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,GAAG,EAAE;AACtD,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AAClB,YAAA,QAAQ,EAAE;;AAEZ,QAAA,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE;AAC7B,QAAA,IAAI,MAAM,IAAI,YAAY,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,UAAU,EAAE;AACjF,YAAA,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC;;QAEjC,MAAM,SAAS,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,QAAQ;AAC1C,QAAA,OAAO,CAAC,GAAG,CAAC,OAAO,QAAQ,CAAA,wBAAA,CAA0B,CAAC;AACtD,QAAA,OAAO,CAAC,GAAG,CAAC,CAAA,EAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAe,aAAA,CAAA,CAAC;AAEnD,QAAA,OAAO,IAAI,yBAAyB,CAAC,SAAS,EAAE,QAAQ,CAAC;;AAE5D;;ACtDD,MAAM,oBAAoB,GAAG,UAAU;AAEvC;;;;;;;;;;;;AAYG;AACG,SAAU,gBAAgB,CAAI,GAAoB,EAAA;IACtD,WAAW,CAAC,oBAAoB,EAAE,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC;AAC3D,IAAA,OAAO,GAAG;AACZ;AAEA;;;;AAIG;SACa,iBAAiB,GAAA;AAC/B,IAAA,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC;AACzC;;AC9BA;;;;AAIG;MACU,EAAE,CAAA;AACb;;;;;;;AAOG;AACH,IAAA,OAAO,GAAG,GAAA;AACR,QAAA,OAAO,MAAM,IAAI;;AAGnB;;;;;;;AAOG;IACH,OAAO,GAAG,CAAC,QAAgB,EAAA;QACzB,OAAO,CAAC,YAAY,KAAI;AACtB,YAAA,OAAO,YAAY,CAAC,aAAa,IAAI;kBACjC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,QAAQ;kBACnD,KAAK;AACX,SAAC;;AAGH;;;;;;;AAOG;IACH,OAAO,SAAS,CAAC,IAAe,EAAA;AAC9B,QAAA,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC,cAAe,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;AAEvE;AAED,SAAS,cAAc,CAAC,CAAM,EAAE,QAAgB,EAAA;IAC9C,IAAID,OAAM,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;AAC7B,QAAA,QACE,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;aAChC,CAAC,CAAC,iBAAiB,IAAI,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AACtD,aAAC,CAAC,CAAC,qBAAqB,IAAI,CAAC,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;;AAIlE,IAAA,OAAO,KAAK;AACd;;AC5DA;AAgBA;;AAEG;AACH,MAAM,WAAW,GAAG;;AAElB,IAAA,KAAK,EAAE,IAAI;AACX,IAAA,UAAU,EAAE,IAAI;AAChB,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,UAAU,EAAE,IAAI;AAChB,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,SAAS,EAAE,IAAI;;AAEf,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,UAAU,EAAE,IAAI;AAChB,IAAA,aAAa,EAAE,IAAI;AACnB,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,UAAU,EAAE,IAAI;;AAEhB,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,SAAS,EAAE,IAAI;;AAEf,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,aAAa,EAAE,IAAI;AACnB,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,cAAc,EAAE,IAAI;;AAEpB,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,WAAW,EAAE,IAAI;;AAEjB,IAAA,KAAK,EAAE,IAAI;AACX,IAAA,WAAW,EAAE,IAAI;CAClB;AAED;;;;;;;;AAQG;MACU,qBAAqB,GAAG,IAAI,cAAc,CACrD,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,qBAAqB,GAAG,EAAE;AAY5E;;;;;;;;AAQG;MACU,aAAa,GAAG,IAAI,cAAc,CAC7C,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,cAAc,GAAG,EAAE;AASrE;;;;;;AAMG;MAEU,mBAAmB,CAAA;AAC9B;;;;AAIG;IACH,MAAM,GAAa,EAAE;AAErB;;;;;;;;;;;;;;;AAeG;IACH,SAAS,GAA4B,EAAE;AAEvC;;;;;;AAMG;AACH,IAAA,OAAO;AAWP;;;;;AAKG;AACH,IAAA,WAAW,CAAC,OAAoB,EAAA;QAC9B,MAAM,EAAE,GAAG,IAAI,MAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC;AAE7C,QAAA,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAC,MAAM,EAAE,IAAI,EAAC,CAAC;AACnC,QAAA,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAC,MAAM,EAAE,IAAI,EAAC,CAAC;AAEpC,QAAA,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;AACtC,YAAA,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;;AAGlD,QAAA,OAAO,EAAE;;kHA5DA,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAnB,mBAAmB,EAAA,CAAA;;sGAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAD/B;;AAiED;;;;AAIG;AAEG,MAAO,oBAAqB,SAAQ,kBAAkB,CAAA;AAKjB,IAAA,OAAA;AAC/B,IAAA,SAAA;AACmC,IAAA,MAAA;IANrC,cAAc,GAAyB,IAAI;AAEnD,IAAA,WAAA,CACoB,GAAQ,EACa,OAA4B,EAC3D,SAAmB,EACgB,MAA4B,EAAA;QAEvE,KAAK,CAAC,GAAG,CAAC;QAJ6B,IAAO,CAAA,OAAA,GAAP,OAAO;QACtC,IAAS,CAAA,SAAA,GAAT,SAAS;QAC0B,IAAM,CAAA,MAAA,GAAN,MAAM;;AAK1C,IAAA,QAAQ,CAAC,SAAiB,EAAA;AACjC,QAAA,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE;AAC1F,YAAA,OAAO,KAAK;;QAGd,IAAI,CAAE,MAAc,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAC3C,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;;;gBAGjD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAACE,QAAO,CAAC;AAC5C,gBAAA,QAAQ,CAAC,IAAI,CACX,CAAA,KAAA,EAAQ,SAAS,CAAmD,iDAAA,CAAA;AAClE,oBAAA,CAAA,+CAAA,CAAiD,CACpD;;AAEH,YAAA,OAAO,KAAK;;AAGd,QAAA,OAAO,IAAI;;AAGJ,IAAA,gBAAgB,CAAC,OAAoB,EAAE,SAAiB,EAAE,OAAiB,EAAA;QAClF,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AACnC,QAAA,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE;;;QAInC,IAAI,CAAE,MAAc,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE;AAC1C,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,IAAI,CAAC,MAAO,EAAE,CAAC;;;;YAIzF,IAAI,kBAAkB,GAAG,KAAK;YAC9B,IAAI,UAAU,GAAa,MAAK;gBAC9B,kBAAkB,GAAG,IAAI;AAC3B,aAAC;AAED,YAAA,IAAI,CAAC,iBAAiB,CAAC,MACrB,IAAI,CAAC,cAAe,CAAC,IAAI,CAAC,MAAK;;AAE7B,gBAAA,IAAI,CAAE,MAAc,CAAC,MAAM,EAAE;AAC3B,oBAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;wBACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAACA,QAAO,CAAC;AAC5C,wBAAA,QAAQ,CAAC,IAAI,CAAC,CAAA,iEAAA,CAAmE,CAAC;;AAEpF,oBAAA,UAAU,GAAG,MAAK,GAAG;oBACrB;;gBAGF,IAAI,CAAC,kBAAkB,EAAE;;;;oBAIvB,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC;;AAEnE,aAAC,CAAC,CAAC,KAAK,CAAC,MAAK;AACZ,gBAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;oBACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAACA,QAAO,CAAC;AAC5C,oBAAA,QAAQ,CAAC,IAAI,CACX,CAAA,KAAA,EAAQ,SAAS,CAA6C,2CAAA,CAAA;AAC5D,wBAAA,CAAA,wBAAA,CAA0B,CAC7B;;AAEH,gBAAA,UAAU,GAAG,MAAK,GAAG;aACtB,CAAC,CACH;;;;AAKD,YAAA,OAAO,MAAK;AACV,gBAAA,UAAU,EAAE;AACd,aAAC;;AAGH,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAK;;YAEjC,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC;YAC5C,MAAM,QAAQ,GAAG,UAAU,QAAqB,EAAA;gBAC9C,IAAI,CAAC,UAAU,CAAC,YAAA;oBACd,OAAO,CAAC,QAAQ,CAAC;AACnB,iBAAC,CAAC;AACJ,aAAC;AACD,YAAA,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;AAC1B,YAAA,OAAO,MAAK;AACV,gBAAA,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC;;AAE3B,gBAAA,IAAI,OAAO,EAAE,CAAC,OAAO,KAAK,UAAU,EAAE;oBACpC,EAAE,CAAC,OAAO,EAAE;;AAEhB,aAAC;AACH,SAAC,CAAC;;AAGJ,IAAA,aAAa,CAAC,SAAiB,EAAA;AAC7B,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;;AA3GzC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,EAIrB,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,QAAQ,EACR,EAAA,EAAA,KAAA,EAAA,qBAAqB,qCAET,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAPxB,oBAAoB,EAAA,CAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC;;0BAKI,MAAM;2BAAC,QAAQ;;0BACf,MAAM;2BAAC,qBAAqB;;0BAE5B;;0BAAY,MAAM;2BAAC,aAAa;;AAwGrC;;;;;;;;;;;;AAYG;MAYU,YAAY,CAAA;kHAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;mHAAZ,YAAY,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,EAVZ,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,qBAAqB;AAC9B,gBAAA,QAAQ,EAAE,oBAAoB;AAC9B,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,IAAI,EAAE,CAAC,QAAQ,EAAE,qBAAqB,EAAE,QAAQ,EAAE,CAAC,IAAI,QAAQ,EAAE,EAAE,aAAa,CAAC,CAAC;AACnF,aAAA;AACD,YAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,mBAAmB,EAAC;AAChE,SAAA,EAAA,CAAA;;sGAEU,YAAY,EAAA,UAAA,EAAA,CAAA;kBAXxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,qBAAqB;AAC9B,4BAAA,QAAQ,EAAE,oBAAoB;AAC9B,4BAAA,KAAK,EAAE,IAAI;AACX,4BAAA,IAAI,EAAE,CAAC,QAAQ,EAAE,qBAAqB,EAAE,QAAQ,EAAE,CAAC,IAAI,QAAQ,EAAE,EAAE,aAAa,CAAC,CAAC;AACnF,yBAAA;AACD,wBAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,mBAAmB,EAAC;AAChE,qBAAA;AACF,iBAAA;;;ACpPD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;MAEmB,YAAY,CAAA;kHAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAZ,YAAY,EAAA,UAAA,EADT,MAAM,EAAA,WAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAgC,gBAAgB,CAAA,EAAA,CAAA;;sGACzD,YAAY,EAAA,UAAA,EAAA,CAAA;kBADjC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,gBAAgB,CAAC,EAAC;;AA2D3E,MAAO,gBAAiB,SAAQ,YAAY,CAAA;AACV,IAAA,IAAA;AAAtC,IAAA,WAAA,CAAsC,IAAS,EAAA;AAC7C,QAAA,KAAK,EAAE;QAD6B,IAAI,CAAA,IAAA,GAAJ,IAAI;;IAIjC,QAAQ,CAAC,GAAoB,EAAE,KAAgC,EAAA;QACtE,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI;QAC9B,QAAQ,GAAG;YACT,KAAK,eAAe,CAAC,IAAI;AACvB,gBAAA,OAAO,KAAe;YACxB,KAAK,eAAe,CAAC,IAAI;AACvB,gBAAA,IAAIC,gCAA8B,CAAC,KAAK,EAAA,MAAA,uBAAkB,EAAE;AAC1D,oBAAA,OAAOC,gBAAe,CAAC,KAAK,CAAC;;AAE/B,gBAAA,OAAOC,cAAa,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE;YAC3D,KAAK,eAAe,CAAC,KAAK;AACxB,gBAAA,IAAIF,gCAA8B,CAAC,KAAK,EAAA,OAAA,wBAAmB,EAAE;AAC3D,oBAAA,OAAOC,gBAAe,CAAC,KAAK,CAAC;;AAE/B,gBAAA,OAAO,KAAe;YACxB,KAAK,eAAe,CAAC,MAAM;AACzB,gBAAA,IAAID,gCAA8B,CAAC,KAAK,EAAA,QAAA,yBAAoB,EAAE;AAC5D,oBAAA,OAAOC,gBAAe,CAAC,KAAK,CAAC;;gBAE/B,MAAM,IAAIE,aAAY,CAAA,IAAA,oDAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,oBAAA,uCAAuC,CAC1C;YACH,KAAK,eAAe,CAAC,GAAG;AACtB,gBAAA,IAAIH,gCAA8B,CAAC,KAAK,EAAA,KAAA,sBAAiB,EAAE;AACzD,oBAAA,OAAOC,gBAAe,CAAC,KAAK,CAAC;;AAE/B,gBAAA,OAAOG,aAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACpC,KAAK,eAAe,CAAC,YAAY;AAC/B,gBAAA,IAAIJ,gCAA8B,CAAC,KAAK,EAAA,aAAA,8BAAyB,EAAE;AACjE,oBAAA,OAAOC,gBAAe,CAAC,KAAK,CAAC;;gBAE/B,MAAM,IAAIE,aAAY,CAAA,IAAA,0DAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;oBAC5C,CAAoD,iDAAA,EAAAE,iBAAgB,CAAG,CAAA,CAAA,CAC1E;AACH,YAAA;gBACE,MAAM,IAAIF,aAAY,CAAA,IAAA,qDAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,oBAAA,CAAA,2BAAA,EAA8B,GAAG,CAAA,MAAA,EAASE,iBAAgB,CAAA,CAAA,CAAG,CAChE;;;AAIE,IAAA,uBAAuB,CAAC,KAAa,EAAA;AAC5C,QAAA,OAAOC,4BAA2B,CAAC,KAAK,CAAC;;AAElC,IAAA,wBAAwB,CAAC,KAAa,EAAA;AAC7C,QAAA,OAAOC,6BAA4B,CAAC,KAAK,CAAC;;AAEnC,IAAA,yBAAyB,CAAC,KAAa,EAAA;AAC9C,QAAA,OAAOC,8BAA6B,CAAC,KAAK,CAAC;;AAEpC,IAAA,sBAAsB,CAAC,KAAa,EAAA;AAC3C,QAAA,OAAOC,2BAA0B,CAAC,KAAK,CAAC;;AAEjC,IAAA,8BAA8B,CAAC,KAAa,EAAA;AACnD,QAAA,OAAOC,mCAAkC,CAAC,KAAK,CAAC;;AAjEvC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,kBACP,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AADjB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA;;sGAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;0BAEjB,MAAM;2BAAC,QAAQ;;;AC1I9B;;;;;AAKG;IACS;AAAZ,CAAA,UAAY,oBAAoB,EAAA;AAC9B,IAAA,oBAAA,CAAA,oBAAA,CAAA,qBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,qBAAmB;AACnB,IAAA,oBAAA,CAAA,oBAAA,CAAA,0BAAA,CAAA,GAAA,CAAA,CAAA,GAAA,0BAAwB;AACxB,IAAA,oBAAA,CAAA,oBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW;AACX,IAAA,oBAAA,CAAA,oBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW;AACX,IAAA,oBAAA,CAAA,oBAAA,CAAA,sBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,sBAAoB;AACtB,CAAC,EANW,oBAAoB,KAApB,oBAAoB,GAM/B,EAAA,CAAA,CAAA;AAYD;;AAEG;AACH,SAAS,gBAAgB,CACvB,KAAkB,EAClB,aAAyB,EAAE,EAC3B,WAAoB,EAAE,EAAA;AAEtB,IAAA,OAAO,EAAC,KAAK,EAAE,UAAU,EAAC;AAC5B;AAEA;;;;;;;AAOG;SACa,uBAAuB,GAAA;;;AAGrC,IAAA,OAAO,gBAAgB,CAAC,oBAAoB,CAAC,mBAAmB,CAAC;AACnE;AAEA;;;;;;;;;AASG;AACG,SAAU,4BAA4B,CAC1C,OAAiC,EAAA;;IAGjC,OAAO,gBAAgB,CACrB,oBAAoB,CAAC,wBAAwB,EAC7CC,sBAAsB,CAAC,OAAO,CAAC,CAChC;AACH;AAEA;;;;AAIG;SACa,eAAe,GAAA;IAC7B,OAAO,gBAAgB,CAAC,oBAAoB,CAAC,WAAW,EAAEC,gBAAgB,EAAE,CAAC;AAC/E;AAEA;;;;;;;;;;;;;;;;AAgBG;SACa,eAAe,GAAA;IAC7B,OAAO,gBAAgB,CAAC,oBAAoB,CAAC,WAAW,EAAEC,gBAAgB,EAAE,CAAC;AAC/E;AAEA;;;;;;;;;;;;;;AAcG;SACa,wBAAwB,GAAA;IACtC,OAAO,gBAAgB,CAAC,oBAAoB,CAAC,oBAAoB,EAAEC,yBAAyB,EAAE,CAAC;AACjG;AAEA;;;;AAIG;AACH,SAAS,kCAAkC,GAAA;IACzC,OAAO;AACL,QAAA;AACE,YAAA,OAAO,EAAE,uBAAuB;YAChC,QAAQ,EAAE,MAAK;AACb,gBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,gBAAA,MAAM,UAAU,GAAG,MAAM,CAACC,iBAAgB,CAAC;;;gBAG3C,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,WAAW,KAAK,MAAM,EAAE;AAChD,oBAAA,MAAM,OAAO,GAAG,MAAM,CAAChB,QAAO,CAAC;AAC/B,oBAAA,MAAM,OAAO,GAAGiB,mBAAkB,CAAA,CAAA,IAAA,qDAEhC,iEAAiE;wBAC/D,uDAAuD;AACvD,wBAAA,kDAAkD,CACrD;AACD,oBAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;;aAExB;AACD,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA;KACF;AACH;AAEA;;;;AAIG;AACH,SAAS,+CAA+C,GAAA;IACtD,OAAO;AACL,QAAA;AACE,YAAA,OAAO,EAAE,uBAAuB;YAChC,QAAQ,EAAE,MAAK;AACb,gBAAA,MAAM,kCAAkC,GAAG,MAAM,CAACC,uCAAsC,EAAE;AACxF,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA,CAAC;gBAEF,IAAI,kCAAkC,EAAE;AACtC,oBAAA,MAAM,OAAO,GAAG,MAAM,CAAClB,QAAO,CAAC;AAC/B,oBAAA,MAAM,OAAO,GAAGiB,mBAAkB,CAAA,IAAA,wDAEhC,mFAAmF;AACjF,wBAAA,oDAAoD,CACvD;AACD,oBAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;;aAExB;AACD,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA;KACF;AACH;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDG;AACa,SAAA,sBAAsB,CACpC,GAAG,QAAkD,EAAA;IAErD,MAAM,SAAS,GAAe,EAAE;AAChC,IAAA,MAAM,YAAY,GAAG,IAAI,GAAG,EAAwB;IAEpD,KAAK,MAAM,EAAC,UAAU,EAAE,KAAK,EAAC,IAAI,QAAQ,EAAE;AAC1C,QAAA,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC;AAEvB,QAAA,IAAI,UAAU,CAAC,MAAM,EAAE;AACrB,YAAA,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;;;IAI9B,MAAM,2BAA2B,GAAG,YAAY,CAAC,GAAG,CAClD,oBAAoB,CAAC,wBAAwB,CAC9C;IAED,IACE,OAAO,SAAS,KAAK,WAAW;QAChC,SAAS;AACT,QAAA,YAAY,CAAC,GAAG,CAAC,oBAAoB,CAAC,mBAAmB,CAAC;AAC1D,QAAA,2BAA2B,EAC3B;AACA,QAAA,MAAM,IAAIb,aAAY,CAEpB,IAAA,wDAAA,sKAAsK,CACvK;;AAGH,IAAA,OAAO,wBAAwB,CAAC;AAC9B,QAAA,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,kCAAkC,EAAE,GAAG,EAAE;AACzF,QAAA,OAAO,SAAS,KAAK,WAAW,IAAI;cAChC,+CAA+C;AACjD,cAAE,EAAE;AACN,QAAAe,iBAAgB,EAAE;QAClB,YAAY,CAAC,GAAG,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,IAAI;AAC5D,cAAE;AACF,cAAEP,sBAAsB,CAAC,EAAE,CAAC;QAC9B,SAAS;AACV,KAAA,CAAC;AACJ;;AC/RA;;;;AAIG;AAIH;;AAEG;AACU,MAAA,OAAO,mBAAmB,IAAI,OAAO,CAAC,mBAAmB;;;;"}