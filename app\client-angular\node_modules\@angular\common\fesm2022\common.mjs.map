{"version": 3, "file": "common.mjs", "sources": ["../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/i18n/locale_data.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/platform_id.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/version.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/viewport_scroller.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/image_loaders/constants.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/url.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/image_loaders/image_loader.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/image_loaders/cloudflare_loader.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/image_loaders/cloudinary_loader.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/image_loaders/imagekit_loader.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/image_loaders/imgix_loader.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/image_loaders/netlify_loader.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/error_helper.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/asserts.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/lcp_image_observer.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/preconnect_link_checker.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/tokens.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/preload-link-creator.ts", "../../../../../k8-fastbuild-ST-199a4f3c4e20/bin/packages/common/src/directives/ng_optimized_image/ng_optimized_image.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ɵregisterLocaleData} from '@angular/core';\n\n/**\n * Register global data to be used internally by Angular. See the\n * [\"I18n guide\"](guide/i18n/format-data-locale) to know how to import additional locale\n * data.\n *\n * The signature registerLocaleData(data: any, extraData?: any) is deprecated since v5.1\n *\n * @publicApi\n */\nexport function registerLocaleData(data: any, localeId?: string | any, extraData?: any): void {\n  return ɵregisterLocaleData(data, localeId, extraData);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport const PLATFORM_BROWSER_ID = 'browser';\nexport const PLATFORM_SERVER_ID = 'server';\n\n/**\n * Returns whether a platform id represents a browser platform.\n * @publicApi\n */\nexport function isPlatformBrowser(platformId: Object): boolean {\n  return platformId === PLATFORM_BROWSER_ID;\n}\n\n/**\n * Returns whether a platform id represents a server platform.\n * @publicApi\n */\nexport function isPlatformServer(platformId: Object): boolean {\n  return platformId === PLATFORM_SERVER_ID;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\nimport {Version} from '@angular/core';\n\n/**\n * @publicApi\n */\nexport const VERSION = /* @__PURE__ */ new Version('20.3.13');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  inject,\n  ɵɵdefineInjectable,\n  DOCUMENT,\n  ɵformatRuntimeError as formatRuntimeError,\n} from '@angular/core';\nimport {RuntimeErrorCode} from './errors';\n\n/**\n * Defines a scroll position manager. Implemented by `BrowserViewportScroller`.\n *\n * @publicApi\n */\nexport abstract class ViewportScroller {\n  // De-sugared tree-shakable injection\n  // See #23917\n  /** @nocollapse */\n  static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n    token: ViewportScroller,\n    providedIn: 'root',\n    factory: () =>\n      typeof ngServerMode !== 'undefined' && ngServerMode\n        ? new NullViewportScroller()\n        : new BrowserViewportScroller(inject(DOCUMENT), window),\n  });\n\n  /**\n   * Configures the top offset used when scrolling to an anchor.\n   * @param offset A position in screen coordinates (a tuple with x and y values)\n   * or a function that returns the top offset position.\n   *\n   */\n  abstract setOffset(offset: [number, number] | (() => [number, number])): void;\n\n  /**\n   * Retrieves the current scroll position.\n   * @returns A position in screen coordinates (a tuple with x and y values).\n   */\n  abstract getScrollPosition(): [number, number];\n\n  /**\n   * Scrolls to a specified position.\n   * @param position A position in screen coordinates (a tuple with x and y values).\n   */\n  abstract scrollToPosition(position: [number, number], options?: ScrollOptions): void;\n\n  /**\n   * Scrolls to an anchor element.\n   * @param anchor The ID of the anchor element.\n   */\n  abstract scrollToAnchor(anchor: string, options?: ScrollOptions): void;\n\n  /**\n   * Disables automatic scroll restoration provided by the browser.\n   * See also [window.history.scrollRestoration\n   * info](https://developers.google.com/web/updates/2015/09/history-api-scroll-restoration).\n   */\n  abstract setHistoryScrollRestoration(scrollRestoration: 'auto' | 'manual'): void;\n}\n\n/**\n * Manages the scroll position for a browser window.\n */\nexport class BrowserViewportScroller implements ViewportScroller {\n  private offset: () => [number, number] = () => [0, 0];\n\n  constructor(\n    private document: Document,\n    private window: Window,\n  ) {}\n\n  /**\n   * Configures the top offset used when scrolling to an anchor.\n   * @param offset A position in screen coordinates (a tuple with x and y values)\n   * or a function that returns the top offset position.\n   *\n   */\n  setOffset(offset: [number, number] | (() => [number, number])): void {\n    if (Array.isArray(offset)) {\n      this.offset = () => offset;\n    } else {\n      this.offset = offset;\n    }\n  }\n\n  /**\n   * Retrieves the current scroll position.\n   * @returns The position in screen coordinates.\n   */\n  getScrollPosition(): [number, number] {\n    return [this.window.scrollX, this.window.scrollY];\n  }\n\n  /**\n   * Sets the scroll position.\n   * @param position The new position in screen coordinates.\n   */\n  scrollToPosition(position: [number, number], options?: ScrollOptions): void {\n    this.window.scrollTo({...options, left: position[0], top: position[1]});\n  }\n\n  /**\n   * Scrolls to an element and attempts to focus the element.\n   *\n   * Note that the function name here is misleading in that the target string may be an ID for a\n   * non-anchor element.\n   *\n   * @param target The ID of an element or name of the anchor.\n   *\n   * @see https://html.spec.whatwg.org/#the-indicated-part-of-the-document\n   * @see https://html.spec.whatwg.org/#scroll-to-fragid\n   */\n  scrollToAnchor(target: string, options?: ScrollOptions): void {\n    const elSelected = findAnchorFromDocument(this.document, target);\n\n    if (elSelected) {\n      this.scrollToElement(elSelected, options);\n      // After scrolling to the element, the spec dictates that we follow the focus steps for the\n      // target. Rather than following the robust steps, simply attempt focus.\n      //\n      // @see https://html.spec.whatwg.org/#get-the-focusable-area\n      // @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLOrForeignElement/focus\n      // @see https://html.spec.whatwg.org/#focusable-area\n      elSelected.focus();\n    }\n  }\n\n  /**\n   * Disables automatic scroll restoration provided by the browser.\n   */\n  setHistoryScrollRestoration(scrollRestoration: 'auto' | 'manual'): void {\n    try {\n      this.window.history.scrollRestoration = scrollRestoration;\n    } catch {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.SCROLL_RESTORATION_UNSUPPORTED,\n          ngDevMode &&\n            'Failed to set `window.history.scrollRestoration`. ' +\n              'This may occur when:\\n' +\n              '• The script is running inside a sandboxed iframe\\n' +\n              '• The window is partially navigated or inactive\\n' +\n              '• The script is executed in an untrusted or special context (e.g., test runners, browser extensions, or content previews)\\n' +\n              'Scroll position may not be preserved across navigation.',\n        ),\n      );\n    }\n  }\n\n  /**\n   * Scrolls to an element using the native offset and the specified offset set on this scroller.\n   *\n   * The offset can be used when we know that there is a floating header and scrolling naively to an\n   * element (ex: `scrollIntoView`) leaves the element hidden behind the floating header.\n   */\n  private scrollToElement(el: HTMLElement, options?: ScrollOptions): void {\n    const rect = el.getBoundingClientRect();\n    const left = rect.left + this.window.pageXOffset;\n    const top = rect.top + this.window.pageYOffset;\n    const offset = this.offset();\n    this.window.scrollTo({\n      ...options,\n      left: left - offset[0],\n      top: top - offset[1],\n    });\n  }\n}\n\nfunction findAnchorFromDocument(document: Document, target: string): HTMLElement | null {\n  const documentResult = document.getElementById(target) || document.getElementsByName(target)[0];\n\n  if (documentResult) {\n    return documentResult;\n  }\n\n  // `getElementById` and `getElementsByName` won't pierce through the shadow DOM so we\n  // have to traverse the DOM manually and do the lookup through the shadow roots.\n  if (\n    typeof document.createTreeWalker === 'function' &&\n    document.body &&\n    typeof document.body.attachShadow === 'function'\n  ) {\n    const treeWalker = document.createTreeWalker(document.body, NodeFilter.SHOW_ELEMENT);\n    let currentNode = treeWalker.currentNode as HTMLElement | null;\n\n    while (currentNode) {\n      const shadowRoot = currentNode.shadowRoot;\n\n      if (shadowRoot) {\n        // Note that `ShadowRoot` doesn't support `getElementsByName`\n        // so we have to fall back to `querySelector`.\n        const result =\n          shadowRoot.getElementById(target) || shadowRoot.querySelector(`[name=\"${target}\"]`);\n        if (result) {\n          return result;\n        }\n      }\n\n      currentNode = treeWalker.nextNode() as HTMLElement | null;\n    }\n  }\n\n  return null;\n}\n\n/**\n * Provides an empty implementation of the viewport scroller.\n */\nexport class NullViewportScroller implements ViewportScroller {\n  /**\n   * Empty implementation\n   */\n  setOffset(offset: [number, number] | (() => [number, number])): void {}\n\n  /**\n   * Empty implementation\n   */\n  getScrollPosition(): [number, number] {\n    return [0, 0];\n  }\n\n  /**\n   * Empty implementation\n   */\n  scrollToPosition(position: [number, number]): void {}\n\n  /**\n   * Empty implementation\n   */\n  scrollToAnchor(anchor: string): void {}\n\n  /**\n   * Empty implementation\n   */\n  setHistoryScrollRestoration(scrollRestoration: 'auto' | 'manual'): void {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Value (out of 100) of the requested quality for placeholder images.\n */\nexport const PLACEHOLDER_QUALITY = '20';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// Converts a string that represents a URL into a URL class instance.\nexport function getUrl(src: string, win: Window): URL {\n  // Don't use a base URL is the URL is absolute.\n  return isAbsoluteUrl(src) ? new URL(src) : new URL(src, win.location.href);\n}\n\n// Checks whether a URL is absolute (i.e. starts with `http://` or `https://`).\nexport function isAbsoluteUrl(src: string): boolean {\n  return /^https?:\\/\\//.test(src);\n}\n\n// Given a URL, extract the hostname part.\n// If a URL is a relative one - the URL is returned as is.\nexport function extractHostname(url: string): string {\n  return isAbsoluteUrl(url) ? new URL(url).hostname : url;\n}\n\nexport function isValidPath(path: unknown): boolean {\n  const isString = typeof path === 'string';\n\n  if (!isString || path.trim() === '') {\n    return false;\n  }\n\n  // Calling new URL() will throw if the path string is malformed\n  try {\n    const url = new URL(path);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nexport function normalizePath(path: string): string {\n  return path.endsWith('/') ? path.slice(0, -1) : path;\n}\n\nexport function normalizeSrc(src: string): string {\n  return src.startsWith('/') ? src.slice(1) : src;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken, Provider, ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../../errors';\nimport {isAbsoluteUrl, isValidPath, normalizePath, normalizeSrc} from '../url';\n\n/**\n * Config options recognized by the image loader function.\n *\n * @see {@link ImageLoader}\n * @see {@link NgOptimizedImage}\n * @publicApi\n */\nexport interface ImageLoaderConfig {\n  /**\n   * Image file name to be added to the image request URL.\n   */\n  src: string;\n  /**\n   * Width of the requested image (to be used when generating srcset).\n   */\n  width?: number;\n  /**\n   * Whether the loader should generate a URL for a small image placeholder instead of a full-sized\n   * image.\n   */\n  isPlaceholder?: boolean;\n  /**\n   * Additional user-provided parameters for use by the ImageLoader.\n   */\n  loaderParams?: {[key: string]: any};\n}\n\n/**\n * Represents an image loader function. Image loader functions are used by the\n * NgOptimizedImage directive to produce full image URL based on the image name and its width.\n *\n * @publicApi\n */\nexport type ImageLoader = (config: ImageLoaderConfig) => string;\n\n/**\n * Noop image loader that does no transformation to the original src and just returns it as is.\n * This loader is used as a default one if more specific logic is not provided in an app config.\n *\n * @see {@link ImageLoader}\n * @see {@link NgOptimizedImage}\n */\nexport const noopImageLoader = (config: ImageLoaderConfig) => config.src;\n\n/**\n * Metadata about the image loader.\n */\nexport type ImageLoaderInfo = {\n  name: string;\n  testUrl: (url: string) => boolean;\n};\n\n/**\n * Injection token that configures the image loader function.\n *\n * @see {@link ImageLoader}\n * @see {@link NgOptimizedImage}\n * @publicApi\n */\nexport const IMAGE_LOADER = new InjectionToken<ImageLoader>(ngDevMode ? 'ImageLoader' : '', {\n  providedIn: 'root',\n  factory: () => noopImageLoader,\n});\n\n/**\n * Internal helper function that makes it easier to introduce custom image loaders for the\n * `NgOptimizedImage` directive. It is enough to specify a URL builder function to obtain full DI\n * configuration for a given loader: a DI token corresponding to the actual loader function, plus DI\n * tokens managing preconnect check functionality.\n * @param buildUrlFn a function returning a full URL based on loader's configuration\n * @param exampleUrls example of full URLs for a given loader (used in error messages)\n * @returns a set of DI providers corresponding to the configured image loader\n */\nexport function createImageLoader(\n  buildUrlFn: (path: string, config: ImageLoaderConfig) => string,\n  exampleUrls?: string[],\n) {\n  return function provideImageLoader(path: string) {\n    if (!isValidPath(path)) {\n      throwInvalidPathError(path, exampleUrls || []);\n    }\n\n    // The trailing / is stripped (if provided) to make URL construction (concatenation) easier in\n    // the individual loader functions.\n    path = normalizePath(path);\n\n    const loaderFn = (config: ImageLoaderConfig) => {\n      if (isAbsoluteUrl(config.src)) {\n        // Image loader functions expect an image file name (e.g. `my-image.png`)\n        // or a relative path + a file name (e.g. `/a/b/c/my-image.png`) as an input,\n        // so the final absolute URL can be constructed.\n        // When an absolute URL is provided instead - the loader can not\n        // build a final URL, thus the error is thrown to indicate that.\n        throwUnexpectedAbsoluteUrlError(path, config.src);\n      }\n\n      return buildUrlFn(path, {...config, src: normalizeSrc(config.src)});\n    };\n\n    const providers: Provider[] = [{provide: IMAGE_LOADER, useValue: loaderFn}];\n    return providers;\n  };\n}\n\nfunction throwInvalidPathError(path: unknown, exampleUrls: string[]): never {\n  throw new RuntimeError(\n    RuntimeErrorCode.INVALID_LOADER_ARGUMENTS,\n    ngDevMode &&\n      `Image loader has detected an invalid path (\\`${path}\\`). ` +\n        `To fix this, supply a path using one of the following formats: ${exampleUrls.join(\n          ' or ',\n        )}`,\n  );\n}\n\nfunction throwUnexpectedAbsoluteUrlError(path: string, url: string): never {\n  throw new RuntimeError(\n    RuntimeErrorCode.INVALID_LOADER_ARGUMENTS,\n    ngDevMode &&\n      `Image loader has detected a \\`<img>\\` tag with an invalid \\`ngSrc\\` attribute: ${url}. ` +\n        `This image loader expects \\`ngSrc\\` to be a relative URL - ` +\n        `however the provided value is an absolute URL. ` +\n        `To fix this, provide \\`ngSrc\\` as a path relative to the base URL ` +\n        `configured for this loader (\\`${path}\\`).`,\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Provider} from '@angular/core';\nimport {PLACEHOLDER_QUALITY} from './constants';\nimport {createImageLoader, ImageLoaderConfig} from './image_loader';\n\n/**\n * Function that generates an ImageLoader for [Cloudflare Image\n * Resizing](https://developers.cloudflare.com/images/image-resizing/) and turns it into an Angular\n * provider. Note: Cloudflare has multiple image products - this provider is specifically for\n * Cloudflare Image Resizing; it will not work with Cloudflare Images or Cloudflare Polish.\n *\n * @param path Your domain name, e.g. https://mysite.com\n * @returns Provider that provides an ImageLoader function\n *\n * @publicApi\n */\nexport const provideCloudflareLoader: (path: string) => Provider[] = createImageLoader(\n  createCloudflareUrl,\n  ngDevMode ? ['https://<ZONE>/cdn-cgi/image/<OPTIONS>/<SOURCE-IMAGE>'] : undefined,\n);\n\nfunction createCloudflareUrl(path: string, config: ImageLoaderConfig) {\n  let params = `format=auto`;\n  if (config.width) {\n    params += `,width=${config.width}`;\n  }\n\n  // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n  if (config.isPlaceholder) {\n    params += `,quality=${PLACEHOLDER_QUALITY}`;\n  }\n\n  // Cloudflare image URLs format:\n  // https://developers.cloudflare.com/images/image-resizing/url-format/\n  return `${path}/cdn-cgi/image/${params}/${config.src}`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Provider} from '@angular/core';\nimport {createImageLoader, ImageLoaderConfig, ImageLoaderInfo} from './image_loader';\n\n/**\n * Name and URL tester for Cloudinary.\n */\nexport const cloudinaryLoaderInfo: ImageLoaderInfo = {\n  name: 'Cloudinary',\n  testUrl: isCloudinaryUrl,\n};\n\nconst CLOUDINARY_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.cloudinary\\.com\\/.+/;\n/**\n * Tests whether a URL is from Cloudinary CDN.\n */\nfunction isCloudinaryUrl(url: string): boolean {\n  return CLOUDINARY_LOADER_REGEX.test(url);\n}\n\n/**\n * Function that generates an ImageLoader for Cloudinary and turns it into an Angular provider.\n *\n * @param path Base URL of your Cloudinary images\n * This URL should match one of the following formats:\n * https://res.cloudinary.com/mysite\n * https://mysite.cloudinary.com\n * https://subdomain.mysite.com\n * @returns Set of providers to configure the Cloudinary loader.\n *\n * @publicApi\n */\nexport const provideCloudinaryLoader: (path: string) => Provider[] = createImageLoader(\n  createCloudinaryUrl,\n  ngDevMode\n    ? [\n        'https://res.cloudinary.com/mysite',\n        'https://mysite.cloudinary.com',\n        'https://subdomain.mysite.com',\n      ]\n    : undefined,\n);\n\nfunction createCloudinaryUrl(path: string, config: ImageLoaderConfig) {\n  // Cloudinary image URLformat:\n  // https://cloudinary.com/documentation/image_transformations#transformation_url_structure\n  // Example of a Cloudinary image URL:\n  // https://res.cloudinary.com/mysite/image/upload/c_scale,f_auto,q_auto,w_600/marketing/tile-topics-m.png\n\n  // For a placeholder image, we use the lowest image setting available to reduce the load time\n  // else we use the auto size\n  const quality = config.isPlaceholder ? 'q_auto:low' : 'q_auto';\n\n  let params = `f_auto,${quality}`;\n  if (config.width) {\n    params += `,w_${config.width}`;\n  }\n\n  if (config.loaderParams?.['rounded']) {\n    params += `,r_max`;\n  }\n\n  return `${path}/image/upload/${params}/${config.src}`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Provider} from '@angular/core';\nimport {PLACEHOLDER_QUALITY} from './constants';\nimport {createImageLoader, ImageLoaderConfig, ImageLoaderInfo} from './image_loader';\n\n/**\n * Name and URL tester for ImageKit.\n */\nexport const imageKitLoaderInfo: ImageLoaderInfo = {\n  name: 'ImageKit',\n  testUrl: isImageKitUrl,\n};\n\nconst IMAGE_KIT_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.imagekit\\.io\\/.+/;\n/**\n * Tests whether a URL is from ImageKit CDN.\n */\nfunction isImageKitUrl(url: string): boolean {\n  return IMAGE_KIT_LOADER_REGEX.test(url);\n}\n\n/**\n * Function that generates an ImageLoader for ImageKit and turns it into an Angular provider.\n *\n * @param path Base URL of your ImageKit images\n * This URL should match one of the following formats:\n * https://ik.imagekit.io/myaccount\n * https://subdomain.mysite.com\n * @returns Set of providers to configure the ImageKit loader.\n *\n * @publicApi\n */\nexport const provideImageKitLoader: (path: string) => Provider[] = createImageLoader(\n  createImagekitUrl,\n  ngDevMode ? ['https://ik.imagekit.io/mysite', 'https://subdomain.mysite.com'] : undefined,\n);\n\nexport function createImagekitUrl(path: string, config: ImageLoaderConfig): string {\n  // Example of an ImageKit image URL:\n  // https://ik.imagekit.io/demo/tr:w-300,h-300/medium_cafe_B1iTdD0C.jpg\n  const {src, width} = config;\n  const params: string[] = [];\n\n  if (width) {\n    params.push(`w-${width}`);\n  }\n\n  // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n  if (config.isPlaceholder) {\n    params.push(`q-${PLACEHOLDER_QUALITY}`);\n  }\n\n  const urlSegments = params.length ? [path, `tr:${params.join(',')}`, src] : [path, src];\n  const url = new URL(urlSegments.join('/'));\n  return url.href;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Provider} from '@angular/core';\nimport {PLACEHOLDER_QUALITY} from './constants';\nimport {createImageLoader, ImageLoaderConfig, ImageLoaderInfo} from './image_loader';\n\n/**\n * Name and URL tester for Imgix.\n */\nexport const imgixLoaderInfo: ImageLoaderInfo = {\n  name: 'Imgix',\n  testUrl: isImgixUrl,\n};\n\nconst IMGIX_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.imgix\\.net\\/.+/;\n/**\n * Tests whether a URL is from Imgix CDN.\n */\nfunction isImgixUrl(url: string): boolean {\n  return IMGIX_LOADER_REGEX.test(url);\n}\n\n/**\n * Function that generates an ImageLoader for Imgix and turns it into an Angular provider.\n *\n * @param path path to the desired Imgix origin,\n * e.g. https://somepath.imgix.net or https://images.mysite.com\n * @returns Set of providers to configure the Imgix loader.\n *\n * @publicApi\n */\nexport const provideImgixLoader: (path: string) => Provider[] = createImageLoader(\n  createImgixUrl,\n  ngDevMode ? ['https://somepath.imgix.net/'] : undefined,\n);\n\nfunction createImgixUrl(path: string, config: ImageLoaderConfig) {\n  const url = new URL(`${path}/${config.src}`);\n  // This setting ensures the smallest allowable format is set.\n  url.searchParams.set('auto', 'format');\n  if (config.width) {\n    url.searchParams.set('w', config.width.toString());\n  }\n\n  // When requesting a placeholder image we ask a low quality image to reduce the load time.\n  if (config.isPlaceholder) {\n    url.searchParams.set('q', PLACEHOLDER_QUALITY);\n  }\n  return url.href;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Provider,\n  ɵformatRuntimeError as formatRuntimeError,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../../errors';\nimport {isAbsoluteUrl, isValidPath} from '../url';\n\nimport {IMAGE_LOADER, ImageLoaderConfig, ImageLoaderInfo} from './image_loader';\nimport {PLACEHOLDER_QUALITY} from './constants';\n\n/**\n * Name and URL tester for Netlify.\n */\nexport const netlifyLoaderInfo: ImageLoaderInfo = {\n  name: 'Netlify',\n  testUrl: isNetlifyUrl,\n};\n\nconst NETLIFY_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.netlify\\.app\\/.+/;\n\n/**\n * Tests whether a URL is from a Netlify site. This won't catch sites with a custom domain,\n * but it's a good start for sites in development. This is only used to warn users who haven't\n * configured an image loader.\n */\nfunction isNetlifyUrl(url: string): boolean {\n  return NETLIFY_LOADER_REGEX.test(url);\n}\n\n/**\n * Function that generates an ImageLoader for Netlify and turns it into an Angular provider.\n *\n * @param path optional URL of the desired Netlify site. Defaults to the current site.\n * @returns Set of providers to configure the Netlify loader.\n *\n * @publicApi\n */\nexport function provideNetlifyLoader(path?: string) {\n  if (path && !isValidPath(path)) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_LOADER_ARGUMENTS,\n      ngDevMode &&\n        `Image loader has detected an invalid path (\\`${path}\\`). ` +\n          `To fix this, supply either the full URL to the Netlify site, or leave it empty to use the current site.`,\n    );\n  }\n\n  if (path) {\n    const url = new URL(path);\n    path = url.origin;\n  }\n\n  const loaderFn = (config: ImageLoaderConfig) => {\n    return createNetlifyUrl(config, path);\n  };\n\n  const providers: Provider[] = [{provide: IMAGE_LOADER, useValue: loaderFn}];\n  return providers;\n}\n\nconst validParams = new Map<string, string>([\n  ['height', 'h'],\n  ['fit', 'fit'],\n  ['quality', 'q'],\n  ['q', 'q'],\n  ['position', 'position'],\n]);\n\nfunction createNetlifyUrl(config: ImageLoaderConfig, path?: string) {\n  // Note: `path` can be undefined, in which case we use a fake one to construct a `URL` instance.\n  const url = new URL(path ?? 'https://a/');\n  url.pathname = '/.netlify/images';\n\n  if (!isAbsoluteUrl(config.src) && !config.src.startsWith('/')) {\n    config.src = '/' + config.src;\n  }\n\n  url.searchParams.set('url', config.src);\n\n  if (config.width) {\n    url.searchParams.set('w', config.width.toString());\n  }\n\n  // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n  // If the quality is specified in the loader config - always use provided value.\n  const configQuality = config.loaderParams?.['quality'] ?? config.loaderParams?.['q'];\n  if (config.isPlaceholder && !configQuality) {\n    url.searchParams.set('q', PLACEHOLDER_QUALITY);\n  }\n\n  for (const [param, value] of Object.entries(config.loaderParams ?? {})) {\n    if (validParams.has(param)) {\n      url.searchParams.set(validParams.get(param)!, value.toString());\n    } else {\n      if (ngDevMode) {\n        console.warn(\n          formatRuntimeError(\n            RuntimeErrorCode.INVALID_LOADER_ARGUMENTS,\n            `The Netlify image loader has detected an \\`<img>\\` tag with the unsupported attribute \"\\`${param}\\`\".`,\n          ),\n        );\n      }\n    }\n  }\n  // The \"a\" hostname is used for relative URLs, so we can remove it from the final URL.\n  return url.hostname === 'a' ? url.href.replace(url.origin, '') : url.href;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// Assembles directive details string, useful for error messages.\nexport function imgDirectiveDetails(ngSrc: string, includeNgSrc = true) {\n  const ngSrcInfo = includeNgSrc\n    ? `(activated on an <img> element with the \\`ngSrc=\"${ngSrc}\"\\`) `\n    : '';\n  return `The NgOptimizedImage directive ${ngSrcInfo}has detected that`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../errors';\n\n/**\n * Asserts that the application is in development mode. Throws an error if the application is in\n * production mode. This assert can be used to make sure that there is no dev-mode code invoked in\n * the prod mode accidentally.\n */\nexport function assertDevMode(checkName: string) {\n  if (!ngDevMode) {\n    throw new RuntimeError(\n      RuntimeErrorCode.UNEXPECTED_DEV_MODE_CHECK_IN_PROD_MODE,\n      `Unexpected invocation of the ${checkName} in the prod mode. ` +\n        `Please make sure that the prod mode is enabled for production builds.`,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  inject,\n  Injectable,\n  OnDestroy,\n  ɵformatRuntimeError as formatRuntimeError,\n  DOCUMENT,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../errors';\n\nimport {assertDevMode} from './asserts';\nimport {imgDirectiveDetails} from './error_helper';\nimport {getUrl} from './url';\n\ninterface ObservedImageState {\n  priority: boolean;\n  modified: boolean;\n  alreadyWarnedPriority: boolean;\n  alreadyWarnedModified: boolean;\n}\n\n/**\n * Observer that detects whether an image with `NgOptimizedImage`\n * is treated as a Largest Contentful Paint (LCP) element. If so,\n * asserts that the image has the `priority` attribute.\n *\n * Note: this is a dev-mode only class and it does not appear in prod bundles,\n * thus there is no `ngDevMode` use in the code.\n *\n * Based on https://web.dev/lcp/#measure-lcp-in-javascript.\n */\n@Injectable({providedIn: 'root'})\nexport class LCPImageObserver implements OnDestroy {\n  // Map of full image URLs -> original `ngSrc` values.\n  private images = new Map<string, ObservedImageState>();\n\n  private window: Window | null = inject(DOCUMENT).defaultView;\n  private observer: PerformanceObserver | null = null;\n\n  constructor() {\n    assertDevMode('LCP checker');\n\n    if (\n      (typeof ngServerMode === 'undefined' || !ngServerMode) &&\n      typeof PerformanceObserver !== 'undefined'\n    ) {\n      this.observer = this.initPerformanceObserver();\n    }\n  }\n\n  /**\n   * Inits PerformanceObserver and subscribes to LCP events.\n   * Based on https://web.dev/lcp/#measure-lcp-in-javascript\n   */\n  private initPerformanceObserver(): PerformanceObserver {\n    const observer = new PerformanceObserver((entryList) => {\n      const entries = entryList.getEntries();\n      if (entries.length === 0) return;\n      // We use the latest entry produced by the `PerformanceObserver` as the best\n      // signal on which element is actually an LCP one. As an example, the first image to load on\n      // a page, by virtue of being the only thing on the page so far, is often a LCP candidate\n      // and gets reported by PerformanceObserver, but isn't necessarily the LCP element.\n      const lcpElement = entries[entries.length - 1];\n\n      // Cast to `any` due to missing `element` on the `LargestContentfulPaint` type of entry.\n      // See https://developer.mozilla.org/en-US/docs/Web/API/LargestContentfulPaint\n      const imgSrc = (lcpElement as any).element?.src ?? '';\n\n      // Exclude `data:` and `blob:` URLs, since they are not supported by the directive.\n      if (imgSrc.startsWith('data:') || imgSrc.startsWith('blob:')) return;\n\n      const img = this.images.get(imgSrc);\n      if (!img) return;\n      if (!img.priority && !img.alreadyWarnedPriority) {\n        img.alreadyWarnedPriority = true;\n        logMissingPriorityError(imgSrc);\n      }\n      if (img.modified && !img.alreadyWarnedModified) {\n        img.alreadyWarnedModified = true;\n        logModifiedWarning(imgSrc);\n      }\n    });\n    observer.observe({type: 'largest-contentful-paint', buffered: true});\n    return observer;\n  }\n\n  registerImage(rewrittenSrc: string, originalNgSrc: string, isPriority: boolean) {\n    if (!this.observer) return;\n    const newObservedImageState: ObservedImageState = {\n      priority: isPriority,\n      modified: false,\n      alreadyWarnedModified: false,\n      alreadyWarnedPriority: false,\n    };\n    this.images.set(getUrl(rewrittenSrc, this.window!).href, newObservedImageState);\n  }\n\n  unregisterImage(rewrittenSrc: string) {\n    if (!this.observer) return;\n    this.images.delete(getUrl(rewrittenSrc, this.window!).href);\n  }\n\n  updateImage(originalSrc: string, newSrc: string) {\n    if (!this.observer) return;\n    const originalUrl = getUrl(originalSrc, this.window!).href;\n    const img = this.images.get(originalUrl);\n    if (img) {\n      img.modified = true;\n      this.images.set(getUrl(newSrc, this.window!).href, img);\n      this.images.delete(originalUrl);\n    }\n  }\n\n  ngOnDestroy() {\n    if (!this.observer) return;\n    this.observer.disconnect();\n    this.images.clear();\n  }\n}\n\nfunction logMissingPriorityError(ngSrc: string) {\n  const directiveDetails = imgDirectiveDetails(ngSrc);\n  console.error(\n    formatRuntimeError(\n      RuntimeErrorCode.LCP_IMG_MISSING_PRIORITY,\n      `${directiveDetails} this image is the Largest Contentful Paint (LCP) ` +\n        `element but was not marked \"priority\". This image should be marked ` +\n        `\"priority\" in order to prioritize its loading. ` +\n        `To fix this, add the \"priority\" attribute.`,\n    ),\n  );\n}\n\nfunction logModifiedWarning(ngSrc: string) {\n  const directiveDetails = imgDirectiveDetails(ngSrc);\n  console.warn(\n    formatRuntimeError(\n      RuntimeErrorCode.LCP_IMG_NGSRC_MODIFIED,\n      `${directiveDetails} this image is the Largest Contentful Paint (LCP) ` +\n        `element and has had its \"ngSrc\" attribute modified. This can cause ` +\n        `slower loading performance. It is recommended not to modify the \"ngSrc\" ` +\n        `property on any image which could be the LCP element.`,\n    ),\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  inject,\n  Injectable,\n  InjectionToken,\n  ɵformatRuntimeError as formatRuntimeError,\n  DOCUMENT,\n  OnDestroy,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../errors';\n\nimport {assertDevMode} from './asserts';\nimport {imgDirectiveDetails} from './error_helper';\nimport {extractHostname, getUrl} from './url';\n\n// Set of origins that are always excluded from the preconnect checks.\nconst INTERNAL_PRECONNECT_CHECK_BLOCKLIST = new Set(['localhost', '127.0.0.1', '0.0.0.0', '[::1]']);\n\n/**\n * Injection token to configure which origins should be excluded\n * from the preconnect checks. It can either be a single string or an array of strings\n * to represent a group of origins, for example:\n *\n * ```ts\n *  {provide: PRECONNECT_CHECK_BLOCKLIST, useValue: 'https://your-domain.com'}\n * ```\n *\n * or:\n *\n * ```ts\n *  {provide: PRECONNECT_CHECK_BLOCKLIST,\n *   useValue: ['https://your-domain-1.com', 'https://your-domain-2.com']}\n * ```\n *\n * @publicApi\n */\nexport const PRECONNECT_CHECK_BLOCKLIST = new InjectionToken<Array<string | string[]>>(\n  ngDevMode ? 'PRECONNECT_CHECK_BLOCKLIST' : '',\n);\n\n/**\n * Contains the logic to detect whether an image, marked with the \"priority\" attribute\n * has a corresponding `<link rel=\"preconnect\">` tag in the `document.head`.\n *\n * Note: this is a dev-mode only class, which should not appear in prod bundles,\n * thus there is no `ngDevMode` use in the code.\n */\n@Injectable({providedIn: 'root'})\nexport class PreconnectLinkChecker implements OnDestroy {\n  private document = inject(DOCUMENT);\n\n  /**\n   * Set of <link rel=\"preconnect\"> tags found on this page.\n   * The `null` value indicates that there was no DOM query operation performed.\n   */\n  private preconnectLinks: Set<string> | null = null;\n\n  /*\n   * Keep track of all already seen origin URLs to avoid repeating the same check.\n   */\n  private alreadySeen = new Set<string>();\n\n  private window: Window | null = this.document.defaultView;\n\n  private blocklist = new Set<string>(INTERNAL_PRECONNECT_CHECK_BLOCKLIST);\n\n  constructor() {\n    assertDevMode('preconnect link checker');\n    const blocklist = inject(PRECONNECT_CHECK_BLOCKLIST, {optional: true});\n    if (blocklist) {\n      this.populateBlocklist(blocklist);\n    }\n  }\n\n  private populateBlocklist(origins: Array<string | string[]> | string) {\n    if (Array.isArray(origins)) {\n      deepForEach(origins, (origin) => {\n        this.blocklist.add(extractHostname(origin));\n      });\n    } else {\n      this.blocklist.add(extractHostname(origins));\n    }\n  }\n\n  /**\n   * Checks that a preconnect resource hint exists in the head for the\n   * given src.\n   *\n   * @param rewrittenSrc src formatted with loader\n   * @param originalNgSrc ngSrc value\n   */\n  assertPreconnect(rewrittenSrc: string, originalNgSrc: string): void {\n    if (typeof ngServerMode !== 'undefined' && ngServerMode) return;\n\n    const imgUrl = getUrl(rewrittenSrc, this.window!);\n    if (this.blocklist.has(imgUrl.hostname) || this.alreadySeen.has(imgUrl.origin)) return;\n\n    // Register this origin as seen, so we don't check it again later.\n    this.alreadySeen.add(imgUrl.origin);\n\n    // Note: we query for preconnect links only *once* and cache the results\n    // for the entire lifespan of an application, since it's unlikely that the\n    // list would change frequently. This allows to make sure there are no\n    // performance implications of making extra DOM lookups for each image.\n    this.preconnectLinks ??= this.queryPreconnectLinks();\n\n    if (!this.preconnectLinks.has(imgUrl.origin)) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.PRIORITY_IMG_MISSING_PRECONNECT_TAG,\n          `${imgDirectiveDetails(originalNgSrc)} there is no preconnect tag present for this ` +\n            `image. Preconnecting to the origin(s) that serve priority images ensures that these ` +\n            `images are delivered as soon as possible. To fix this, please add the following ` +\n            `element into the <head> of the document:\\n` +\n            `  <link rel=\"preconnect\" href=\"${imgUrl.origin}\">`,\n        ),\n      );\n    }\n  }\n\n  private queryPreconnectLinks(): Set<string> {\n    const preconnectUrls = new Set<string>();\n    const links = this.document.querySelectorAll<HTMLLinkElement>('link[rel=preconnect]');\n    for (const link of links) {\n      const url = getUrl(link.href, this.window!);\n      preconnectUrls.add(url.origin);\n    }\n    return preconnectUrls;\n  }\n\n  ngOnDestroy() {\n    this.preconnectLinks?.clear();\n    this.alreadySeen.clear();\n  }\n}\n\n/**\n * Invokes a callback for each element in the array. Also invokes a callback\n * recursively for each nested array.\n */\nfunction deepForEach<T>(input: (T | any[])[], fn: (value: T) => void): void {\n  for (let value of input) {\n    Array.isArray(value) ? deepForEach(value, fn) : fn(value);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n/**\n * In SSR scenarios, a preload `<link>` element is generated for priority images.\n * Having a large number of preload tags may negatively affect the performance,\n * so we warn developers (by throwing an error) if the number of preloaded images\n * is above a certain threshold. This const specifies this threshold.\n */\nexport const DEFAULT_PRELOADED_IMAGES_LIMIT = 5;\n\n/**\n * Helps to keep track of priority images that already have a corresponding\n * preload tag (to avoid generating multiple preload tags with the same URL).\n *\n * This Set tracks the original src passed into the `ngSrc` input not the src after it has been\n * run through the specified `IMAGE_LOADER`.\n */\nexport const PRELOADED_IMAGES = new InjectionToken<Set<string>>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'NG_OPTIMIZED_PRELOADED_IMAGES' : '',\n  {\n    providedIn: 'root',\n    factory: () => new Set<string>(),\n  },\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  inject,\n  Injectable,\n  Renderer2,\n  ɵformatRuntimeError as formatRuntimeError,\n  DOCUMENT,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../errors';\n\nimport {DEFAULT_PRELOADED_IMAGES_LIMIT, PRELOADED_IMAGES} from './tokens';\n\n/**\n * @description Contains the logic needed to track and add preload link tags to the `<head>` tag. It\n * will also track what images have already had preload link tags added so as to not duplicate link\n * tags.\n *\n * In dev mode this service will validate that the number of preloaded images does not exceed the\n * configured default preloaded images limit: {@link DEFAULT_PRELOADED_IMAGES_LIMIT}.\n */\n@Injectable({providedIn: 'root'})\nexport class PreloadLinkCreator {\n  private readonly preloadedImages = inject(PRELOADED_IMAGES);\n  private readonly document = inject(DOCUMENT);\n  private errorShown = false;\n\n  /**\n   * @description Add a preload `<link>` to the `<head>` of the `index.html` that is served from the\n   * server while using Angular Universal and SSR to kick off image loads for high priority images.\n   *\n   * The `sizes` (passed in from the user) and `srcset` (parsed and formatted from `ngSrcset`)\n   * properties used to set the corresponding attributes, `imagesizes` and `imagesrcset`\n   * respectively, on the preload `<link>` tag so that the correctly sized image is preloaded from\n   * the CDN.\n   *\n   * {@link https://web.dev/preload-responsive-images/#imagesrcset-and-imagesizes}\n   *\n   * @param renderer The `Renderer2` passed in from the directive\n   * @param src The original src of the image that is set on the `ngSrc` input.\n   * @param srcset The parsed and formatted srcset created from the `ngSrcset` input\n   * @param sizes The value of the `sizes` attribute passed in to the `<img>` tag\n   */\n  createPreloadLinkTag(renderer: Renderer2, src: string, srcset?: string, sizes?: string): void {\n    if (\n      ngDevMode &&\n      !this.errorShown &&\n      this.preloadedImages.size >= DEFAULT_PRELOADED_IMAGES_LIMIT\n    ) {\n      this.errorShown = true;\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.TOO_MANY_PRELOADED_IMAGES,\n          `The \\`NgOptimizedImage\\` directive has detected that more than ` +\n            `${DEFAULT_PRELOADED_IMAGES_LIMIT} images were marked as priority. ` +\n            `This might negatively affect an overall performance of the page. ` +\n            `To fix this, remove the \"priority\" attribute from images with less priority.`,\n        ),\n      );\n    }\n\n    if (this.preloadedImages.has(src)) {\n      return;\n    }\n\n    this.preloadedImages.add(src);\n\n    const preload = renderer.createElement('link');\n    renderer.setAttribute(preload, 'as', 'image');\n    renderer.setAttribute(preload, 'href', src);\n    renderer.setAttribute(preload, 'rel', 'preload');\n    renderer.setAttribute(preload, 'fetchpriority', 'high');\n\n    if (sizes) {\n      renderer.setAttribute(preload, 'imageSizes', sizes);\n    }\n\n    if (srcset) {\n      renderer.setAttribute(preload, 'imageSrcset', srcset);\n    }\n\n    renderer.appendChild(this.document.head, preload);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ApplicationRef,\n  booleanAttribute,\n  ChangeDetectorRef,\n  DestroyRef,\n  Directive,\n  ElementRef,\n  ɵformatRuntimeError as formatRuntimeError,\n  ɵIMAGE_CONFIG as IMAGE_CONFIG,\n  ɵIMAGE_CONFIG_DEFAULTS as IMAGE_CONFIG_DEFAULTS,\n  ɵImageConfig as ImageConfig,\n  inject,\n  Injector,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnChanges,\n  OnInit,\n  ɵperformanceMarkFeature as performanceMarkFeature,\n  Renderer2,\n  ɵRuntimeError as RuntimeError,\n  ɵSafeValue as SafeValue,\n  SimpleChanges,\n  ɵunwrapSafeValue as unwrapSafeValue,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../errors';\n\nimport {imgDirectiveDetails} from './error_helper';\nimport {cloudinaryLoaderInfo} from './image_loaders/cloudinary_loader';\nimport {\n  IMAGE_LOADER,\n  Image<PERSON>oa<PERSON>,\n  ImageLoaderConfig,\n  noopImageLoader,\n} from './image_loaders/image_loader';\nimport {imageKitLoaderInfo} from './image_loaders/imagekit_loader';\nimport {imgixLoaderInfo} from './image_loaders/imgix_loader';\nimport {netlifyLoaderInfo} from './image_loaders/netlify_loader';\nimport {LCPImageObserver} from './lcp_image_observer';\nimport {PreconnectLinkChecker} from './preconnect_link_checker';\nimport {PreloadLinkCreator} from './preload-link-creator';\n\n/**\n * When a Base64-encoded image is passed as an input to the `NgOptimizedImage` directive,\n * an error is thrown. The image content (as a string) might be very long, thus making\n * it hard to read an error message if the entire string is included. This const defines\n * the number of characters that should be included into the error message. The rest\n * of the content is truncated.\n */\nconst BASE64_IMG_MAX_LENGTH_IN_ERROR = 50;\n\n/**\n * RegExpr to determine whether a src in a srcset is using width descriptors.\n * Should match something like: \"100w, 200w\".\n */\nconst VALID_WIDTH_DESCRIPTOR_SRCSET = /^((\\s*\\d+w\\s*(,|$)){1,})$/;\n\n/**\n * RegExpr to determine whether a src in a srcset is using density descriptors.\n * Should match something like: \"1x, 2x, 50x\". Also supports decimals like \"1.5x, 1.50x\".\n */\nconst VALID_DENSITY_DESCRIPTOR_SRCSET = /^((\\s*\\d+(\\.\\d+)?x\\s*(,|$)){1,})$/;\n\n/**\n * Srcset values with a density descriptor higher than this value will actively\n * throw an error. Such densities are not permitted as they cause image sizes\n * to be unreasonably large and slow down LCP.\n */\nexport const ABSOLUTE_SRCSET_DENSITY_CAP = 3;\n\n/**\n * Used only in error message text to communicate best practices, as we will\n * only throw based on the slightly more conservative ABSOLUTE_SRCSET_DENSITY_CAP.\n */\nexport const RECOMMENDED_SRCSET_DENSITY_CAP = 2;\n\n/**\n * Used in generating automatic density-based srcsets\n */\nconst DENSITY_SRCSET_MULTIPLIERS = [1, 2];\n\n/**\n * Used to determine which breakpoints to use on full-width images\n */\nconst VIEWPORT_BREAKPOINT_CUTOFF = 640;\n/**\n * Used to determine whether two aspect ratios are similar in value.\n */\nconst ASPECT_RATIO_TOLERANCE = 0.1;\n\n/**\n * Used to determine whether the image has been requested at an overly\n * large size compared to the actual rendered image size (after taking\n * into account a typical device pixel ratio). In pixels.\n */\nconst OVERSIZED_IMAGE_TOLERANCE = 1000;\n\n/**\n * Used to limit automatic srcset generation of very large sources for\n * fixed-size images. In pixels.\n */\nconst FIXED_SRCSET_WIDTH_LIMIT = 1920;\nconst FIXED_SRCSET_HEIGHT_LIMIT = 1080;\n\n/**\n * Placeholder dimension (height or width) limit in pixels. Angular produces a warning\n * when this limit is crossed.\n */\nconst PLACEHOLDER_DIMENSION_LIMIT = 1000;\n\n/**\n * Used to warn or error when the user provides an overly large dataURL for the placeholder\n * attribute.\n * Character count of Base64 images is 1 character per byte, and base64 encoding is approximately\n * 33% larger than base images, so 4000 characters is around 3KB on disk and 10000 characters is\n * around 7.7KB. Experimentally, 4000 characters is about 20x20px in PNG or medium-quality JPEG\n * format, and 10,000 is around 50x50px, but there's quite a bit of variation depending on how the\n * image is saved.\n */\nexport const DATA_URL_WARN_LIMIT = 4000;\nexport const DATA_URL_ERROR_LIMIT = 10000;\n\n/** Info about built-in loaders we can test for. */\nexport const BUILT_IN_LOADERS = [\n  imgixLoaderInfo,\n  imageKitLoaderInfo,\n  cloudinaryLoaderInfo,\n  netlifyLoaderInfo,\n];\n\n/**\n * Threshold for the PRIORITY_TRUE_COUNT\n */\nconst PRIORITY_COUNT_THRESHOLD = 10;\n\n/**\n * This count is used to log a devMode warning\n * when the count of directive instances with priority=true\n * exceeds the threshold PRIORITY_COUNT_THRESHOLD\n */\nlet IMGS_WITH_PRIORITY_ATTR_COUNT = 0;\n\n/**\n * This function is for testing purpose.\n */\nexport function resetImagePriorityCount() {\n  IMGS_WITH_PRIORITY_ATTR_COUNT = 0;\n}\n\n/**\n * Config options used in rendering placeholder images.\n *\n * @see {@link NgOptimizedImage}\n * @publicApi\n */\nexport interface ImagePlaceholderConfig {\n  blur?: boolean;\n}\n\n/**\n * Directive that improves image loading performance by enforcing best practices.\n *\n * `NgOptimizedImage` ensures that the loading of the Largest Contentful Paint (LCP) image is\n * prioritized by:\n * - Automatically setting the `fetchpriority` attribute on the `<img>` tag\n * - Lazy loading non-priority images by default\n * - Automatically generating a preconnect link tag in the document head\n *\n * In addition, the directive:\n * - Generates appropriate asset URLs if a corresponding `ImageLoader` function is provided\n * - Automatically generates a srcset\n * - Requires that `width` and `height` are set\n * - Warns if `width` or `height` have been set incorrectly\n * - Warns if the image will be visually distorted when rendered\n *\n * @usageNotes\n * The `NgOptimizedImage` directive is marked as [standalone](guide/components/importing) and can\n * be imported directly.\n *\n * Follow the steps below to enable and use the directive:\n * 1. Import it into the necessary NgModule or a standalone Component.\n * 2. Optionally provide an `ImageLoader` if you use an image hosting service.\n * 3. Update the necessary `<img>` tags in templates and replace `src` attributes with `ngSrc`.\n * Using a `ngSrc` allows the directive to control when the `src` gets set, which triggers an image\n * download.\n *\n * Step 1: import the `NgOptimizedImage` directive.\n *\n * ```ts\n * import { NgOptimizedImage } from '@angular/common';\n *\n * // Include it into the necessary NgModule\n * @NgModule({\n *   imports: [NgOptimizedImage],\n * })\n * class AppModule {}\n *\n * // ... or a standalone Component\n * @Component({\n *   imports: [NgOptimizedImage],\n * })\n * class MyStandaloneComponent {}\n * ```\n *\n * Step 2: configure a loader.\n *\n * To use the **default loader**: no additional code changes are necessary. The URL returned by the\n * generic loader will always match the value of \"src\". In other words, this loader applies no\n * transformations to the resource URL and the value of the `ngSrc` attribute will be used as is.\n *\n * To use an existing loader for a **third-party image service**: add the provider factory for your\n * chosen service to the `providers` array. In the example below, the Imgix loader is used:\n *\n * ```ts\n * import {provideImgixLoader} from '@angular/common';\n *\n * // Call the function and add the result to the `providers` array:\n * providers: [\n *   provideImgixLoader(\"https://my.base.url/\"),\n * ],\n * ```\n *\n * The `NgOptimizedImage` directive provides the following functions:\n * - `provideCloudflareLoader`\n * - `provideCloudinaryLoader`\n * - `provideImageKitLoader`\n * - `provideImgixLoader`\n *\n * If you use a different image provider, you can create a custom loader function as described\n * below.\n *\n * To use a **custom loader**: provide your loader function as a value for the `IMAGE_LOADER` DI\n * token.\n *\n * ```ts\n * import {IMAGE_LOADER, ImageLoaderConfig} from '@angular/common';\n *\n * // Configure the loader using the `IMAGE_LOADER` token.\n * providers: [\n *   {\n *      provide: IMAGE_LOADER,\n *      useValue: (config: ImageLoaderConfig) => {\n *        return `https://example.com/${config.src}-${config.width}.jpg`;\n *      }\n *   },\n * ],\n * ```\n *\n * Step 3: update `<img>` tags in templates to use `ngSrc` instead of `src`.\n *\n * ```html\n * <img ngSrc=\"logo.png\" width=\"200\" height=\"100\">\n * ```\n *\n * @publicApi\n * @see [Image Optimization Guide](guide/image-optimization)\n */\n@Directive({\n  selector: 'img[ngSrc]',\n  host: {\n    '[style.position]': 'fill ? \"absolute\" : null',\n    '[style.width]': 'fill ? \"100%\" : null',\n    '[style.height]': 'fill ? \"100%\" : null',\n    '[style.inset]': 'fill ? \"0\" : null',\n    '[style.background-size]': 'placeholder ? \"cover\" : null',\n    '[style.background-position]': 'placeholder ? \"50% 50%\" : null',\n    '[style.background-repeat]': 'placeholder ? \"no-repeat\" : null',\n    '[style.background-image]': 'placeholder ? generatePlaceholder(placeholder) : null',\n    '[style.filter]':\n      'placeholder && shouldBlurPlaceholder(placeholderConfig) ? \"blur(15px)\" : null',\n  },\n})\nexport class NgOptimizedImage implements OnInit, OnChanges {\n  private imageLoader = inject(IMAGE_LOADER);\n  private config: ImageConfig = processConfig(inject(IMAGE_CONFIG));\n  private renderer = inject(Renderer2);\n  private imgElement: HTMLImageElement = inject(ElementRef).nativeElement;\n  private injector = inject(Injector);\n  private destroyRef = inject(DestroyRef);\n\n  // An LCP image observer should be injected only in development mode.\n  // Do not assign it to `null` to avoid having a redundant property in the production bundle.\n  private lcpObserver?: LCPImageObserver;\n\n  /**\n   * Calculate the rewritten `src` once and store it.\n   * This is needed to avoid repetitive calculations and make sure the directive cleanup in the\n   * `ngOnDestroy` does not rely on the `IMAGE_LOADER` logic (which in turn can rely on some other\n   * instance that might be already destroyed).\n   */\n  private _renderedSrc: string | null = null;\n\n  /**\n   * Name of the source image.\n   * Image name will be processed by the image loader and the final URL will be applied as the `src`\n   * property of the image.\n   */\n  @Input({required: true, transform: unwrapSafeUrl}) ngSrc!: string;\n\n  /**\n   * A comma separated list of width or density descriptors.\n   * The image name will be taken from `ngSrc` and combined with the list of width or density\n   * descriptors to generate the final `srcset` property of the image.\n   *\n   * Example:\n   * ```html\n   * <img ngSrc=\"hello.jpg\" ngSrcset=\"100w, 200w\" />  =>\n   * <img src=\"path/hello.jpg\" srcset=\"path/hello.jpg?w=100 100w, path/hello.jpg?w=200 200w\" />\n   * ```\n   */\n  @Input() ngSrcset!: string;\n\n  /**\n   * The base `sizes` attribute passed through to the `<img>` element.\n   * Providing sizes causes the image to create an automatic responsive srcset.\n   */\n  @Input() sizes?: string;\n\n  /**\n   * For responsive images: the intrinsic width of the image in pixels.\n   * For fixed size images: the desired rendered width of the image in pixels.\n   */\n  @Input({transform: numberAttribute}) width: number | undefined;\n\n  /**\n   * For responsive images: the intrinsic height of the image in pixels.\n   * For fixed size images: the desired rendered height of the image in pixels.\n   */\n  @Input({transform: numberAttribute}) height: number | undefined;\n\n  /**\n   * The desired decoding behavior for the image. Defaults to `auto`\n   * if not explicitly set, matching native browser behavior.\n   *\n   * Use `async` to decode the image off the main thread (non-blocking),\n   * `sync` for immediate decoding (blocking), or `auto` to let the\n   * browser decide the optimal strategy.\n   *\n   * [Spec](https://html.spec.whatwg.org/multipage/images.html#image-decoding-hint)\n   */\n  @Input() decoding?: 'sync' | 'async' | 'auto';\n\n  /**\n   * The desired loading behavior (lazy, eager, or auto). Defaults to `lazy`,\n   * which is recommended for most images.\n   *\n   * Warning: Setting images as loading=\"eager\" or loading=\"auto\" marks them\n   * as non-priority images and can hurt loading performance. For images which\n   * may be the LCP element, use the `priority` attribute instead of `loading`.\n   */\n  @Input() loading?: 'lazy' | 'eager' | 'auto';\n\n  /**\n   * Indicates whether this image should have a high priority.\n   */\n  @Input({transform: booleanAttribute}) priority = false;\n\n  /**\n   * Data to pass through to custom loaders.\n   */\n  @Input() loaderParams?: {[key: string]: any};\n\n  /**\n   * Disables automatic srcset generation for this image.\n   */\n  @Input({transform: booleanAttribute}) disableOptimizedSrcset = false;\n\n  /**\n   * Sets the image to \"fill mode\", which eliminates the height/width requirement and adds\n   * styles such that the image fills its containing element.\n   */\n  @Input({transform: booleanAttribute}) fill = false;\n\n  /**\n   * A URL or data URL for an image to be used as a placeholder while this image loads.\n   */\n  @Input({transform: booleanOrUrlAttribute}) placeholder?: string | boolean;\n\n  /**\n   * Configuration object for placeholder settings. Options:\n   *   * blur: Setting this to false disables the automatic CSS blur.\n   */\n  @Input() placeholderConfig?: ImagePlaceholderConfig;\n\n  /**\n   * Value of the `src` attribute if set on the host `<img>` element.\n   * This input is exclusively read to assert that `src` is not set in conflict\n   * with `ngSrc` and that images don't start to load until a lazy loading strategy is set.\n   * @internal\n   */\n  @Input() src?: string;\n\n  /**\n   * Value of the `srcset` attribute if set on the host `<img>` element.\n   * This input is exclusively read to assert that `srcset` is not set in conflict\n   * with `ngSrcset` and that images don't start to load until a lazy loading strategy is set.\n   * @internal\n   */\n  @Input() srcset?: string;\n\n  constructor() {\n    if (ngDevMode) {\n      this.lcpObserver = this.injector.get(LCPImageObserver);\n\n      this.destroyRef.onDestroy(() => {\n        if (!this.priority && this._renderedSrc !== null) {\n          this.lcpObserver!.unregisterImage(this._renderedSrc);\n        }\n      });\n    }\n  }\n\n  /** @docs-private */\n  ngOnInit() {\n    performanceMarkFeature('NgOptimizedImage');\n\n    if (ngDevMode) {\n      const ngZone = this.injector.get(NgZone);\n      assertNonEmptyInput(this, 'ngSrc', this.ngSrc);\n      assertValidNgSrcset(this, this.ngSrcset);\n      assertNoConflictingSrc(this);\n      if (this.ngSrcset) {\n        assertNoConflictingSrcset(this);\n      }\n      assertNotBase64Image(this);\n      assertNotBlobUrl(this);\n      if (this.fill) {\n        assertEmptyWidthAndHeight(this);\n        // This leaves the Angular zone to avoid triggering unnecessary change detection cycles when\n        // `load` tasks are invoked on images.\n        ngZone.runOutsideAngular(() =>\n          assertNonZeroRenderedHeight(this, this.imgElement, this.renderer, this.destroyRef),\n        );\n      } else {\n        assertNonEmptyWidthAndHeight(this);\n        if (this.height !== undefined) {\n          assertGreaterThanZero(this, this.height, 'height');\n        }\n        if (this.width !== undefined) {\n          assertGreaterThanZero(this, this.width, 'width');\n        }\n        // Only check for distorted images when not in fill mode, where\n        // images may be intentionally stretched, cropped or letterboxed.\n        ngZone.runOutsideAngular(() =>\n          assertNoImageDistortion(this, this.imgElement, this.renderer, this.destroyRef),\n        );\n      }\n      assertValidLoadingInput(this);\n      assertValidDecodingInput(this);\n      if (!this.ngSrcset) {\n        assertNoComplexSizes(this);\n      }\n      assertValidPlaceholder(this, this.imageLoader);\n      assertNotMissingBuiltInLoader(this.ngSrc, this.imageLoader);\n      assertNoNgSrcsetWithoutLoader(this, this.imageLoader);\n      assertNoLoaderParamsWithoutLoader(this, this.imageLoader);\n\n      ngZone.runOutsideAngular(() => {\n        this.lcpObserver!.registerImage(this.getRewrittenSrc(), this.ngSrc, this.priority);\n      });\n\n      if (this.priority) {\n        const checker = this.injector.get(PreconnectLinkChecker);\n        checker.assertPreconnect(this.getRewrittenSrc(), this.ngSrc);\n\n        if (typeof ngServerMode !== 'undefined' && !ngServerMode) {\n          const applicationRef = this.injector.get(ApplicationRef);\n          assetPriorityCountBelowThreshold(applicationRef);\n        }\n      }\n    }\n    if (this.placeholder) {\n      this.removePlaceholderOnLoad(this.imgElement);\n    }\n    this.setHostAttributes();\n  }\n\n  private setHostAttributes() {\n    // Must set width/height explicitly in case they are bound (in which case they will\n    // only be reflected and not found by the browser)\n    if (this.fill) {\n      this.sizes ||= '100vw';\n    } else {\n      this.setHostAttribute('width', this.width!.toString());\n      this.setHostAttribute('height', this.height!.toString());\n    }\n\n    this.setHostAttribute('loading', this.getLoadingBehavior());\n    this.setHostAttribute('fetchpriority', this.getFetchPriority());\n    this.setHostAttribute('decoding', this.getDecoding());\n\n    // The `data-ng-img` attribute flags an image as using the directive, to allow\n    // for analysis of the directive's performance.\n    this.setHostAttribute('ng-img', 'true');\n\n    // The `src` and `srcset` attributes should be set last since other attributes\n    // could affect the image's loading behavior.\n    const rewrittenSrcset = this.updateSrcAndSrcset();\n\n    if (this.sizes) {\n      if (this.getLoadingBehavior() === 'lazy') {\n        this.setHostAttribute('sizes', 'auto, ' + this.sizes);\n      } else {\n        this.setHostAttribute('sizes', this.sizes);\n      }\n    } else {\n      if (\n        this.ngSrcset &&\n        VALID_WIDTH_DESCRIPTOR_SRCSET.test(this.ngSrcset) &&\n        this.getLoadingBehavior() === 'lazy'\n      ) {\n        this.setHostAttribute('sizes', 'auto, 100vw');\n      }\n    }\n\n    if (typeof ngServerMode !== 'undefined' && ngServerMode && this.priority) {\n      const preloadLinkCreator = this.injector.get(PreloadLinkCreator);\n      preloadLinkCreator.createPreloadLinkTag(\n        this.renderer,\n        this.getRewrittenSrc(),\n        rewrittenSrcset,\n        this.sizes,\n      );\n    }\n  }\n\n  /** @docs-private */\n  ngOnChanges(changes: SimpleChanges) {\n    if (ngDevMode) {\n      assertNoPostInitInputChange(this, changes, [\n        'ngSrcset',\n        'width',\n        'height',\n        'priority',\n        'fill',\n        'loading',\n        'sizes',\n        'loaderParams',\n        'disableOptimizedSrcset',\n      ]);\n    }\n    if (changes['ngSrc'] && !changes['ngSrc'].isFirstChange()) {\n      const oldSrc = this._renderedSrc;\n      this.updateSrcAndSrcset(true);\n\n      if (ngDevMode) {\n        const newSrc = this._renderedSrc;\n        if (oldSrc && newSrc && oldSrc !== newSrc) {\n          const ngZone = this.injector.get(NgZone);\n          ngZone.runOutsideAngular(() => {\n            this.lcpObserver!.updateImage(oldSrc, newSrc);\n          });\n        }\n      }\n    }\n\n    if (\n      ngDevMode &&\n      changes['placeholder']?.currentValue &&\n      typeof ngServerMode !== 'undefined' &&\n      !ngServerMode\n    ) {\n      assertPlaceholderDimensions(this, this.imgElement);\n    }\n  }\n\n  private callImageLoader(\n    configWithoutCustomParams: Omit<ImageLoaderConfig, 'loaderParams'>,\n  ): string {\n    let augmentedConfig: ImageLoaderConfig = configWithoutCustomParams;\n    if (this.loaderParams) {\n      augmentedConfig.loaderParams = this.loaderParams;\n    }\n    return this.imageLoader(augmentedConfig);\n  }\n\n  private getLoadingBehavior(): string {\n    if (!this.priority && this.loading !== undefined) {\n      return this.loading;\n    }\n    return this.priority ? 'eager' : 'lazy';\n  }\n\n  private getFetchPriority(): string {\n    return this.priority ? 'high' : 'auto';\n  }\n\n  private getDecoding(): string {\n    if (this.priority) {\n      // `sync` means the image is decoded immediately when it's loaded,\n      // reducing the risk of content shifting later (important for LCP).\n      // If we're marking an image as priority, we want it decoded and\n      // painted as early as possible.\n      return 'sync';\n    }\n    // Returns the value of the `decoding` attribute, defaulting to `auto`\n    // if not explicitly provided. This mimics native browser behavior and\n    // avoids breaking changes when no decoding strategy is specified.\n    return this.decoding ?? 'auto';\n  }\n\n  private getRewrittenSrc(): string {\n    // ImageLoaderConfig supports setting a width property. However, we're not setting width here\n    // because if the developer uses rendered width instead of intrinsic width in the HTML width\n    // attribute, the image requested may be too small for 2x+ screens.\n    if (!this._renderedSrc) {\n      const imgConfig = {src: this.ngSrc};\n      // Cache calculated image src to reuse it later in the code.\n      this._renderedSrc = this.callImageLoader(imgConfig);\n    }\n    return this._renderedSrc;\n  }\n\n  private getRewrittenSrcset(): string {\n    const widthSrcSet = VALID_WIDTH_DESCRIPTOR_SRCSET.test(this.ngSrcset);\n    const finalSrcs = this.ngSrcset\n      .split(',')\n      .filter((src) => src !== '')\n      .map((srcStr) => {\n        srcStr = srcStr.trim();\n        const width = widthSrcSet ? parseFloat(srcStr) : parseFloat(srcStr) * this.width!;\n        return `${this.callImageLoader({src: this.ngSrc, width})} ${srcStr}`;\n      });\n    return finalSrcs.join(', ');\n  }\n\n  private getAutomaticSrcset(): string {\n    if (this.sizes) {\n      return this.getResponsiveSrcset();\n    } else {\n      return this.getFixedSrcset();\n    }\n  }\n\n  private getResponsiveSrcset(): string {\n    const {breakpoints} = this.config;\n\n    let filteredBreakpoints = breakpoints!;\n    if (this.sizes?.trim() === '100vw') {\n      // Since this is a full-screen-width image, our srcset only needs to include\n      // breakpoints with full viewport widths.\n      filteredBreakpoints = breakpoints!.filter((bp) => bp >= VIEWPORT_BREAKPOINT_CUTOFF);\n    }\n\n    const finalSrcs = filteredBreakpoints.map(\n      (bp) => `${this.callImageLoader({src: this.ngSrc, width: bp})} ${bp}w`,\n    );\n    return finalSrcs.join(', ');\n  }\n\n  private updateSrcAndSrcset(forceSrcRecalc = false): string | undefined {\n    if (forceSrcRecalc) {\n      // Reset cached value, so that the followup `getRewrittenSrc()` call\n      // will recalculate it and update the cache.\n      this._renderedSrc = null;\n    }\n\n    const rewrittenSrc = this.getRewrittenSrc();\n    this.setHostAttribute('src', rewrittenSrc);\n\n    let rewrittenSrcset: string | undefined = undefined;\n    if (this.ngSrcset) {\n      rewrittenSrcset = this.getRewrittenSrcset();\n    } else if (this.shouldGenerateAutomaticSrcset()) {\n      rewrittenSrcset = this.getAutomaticSrcset();\n    }\n\n    if (rewrittenSrcset) {\n      this.setHostAttribute('srcset', rewrittenSrcset);\n    }\n    return rewrittenSrcset;\n  }\n\n  private getFixedSrcset(): string {\n    const finalSrcs = DENSITY_SRCSET_MULTIPLIERS.map(\n      (multiplier) =>\n        `${this.callImageLoader({\n          src: this.ngSrc,\n          width: this.width! * multiplier,\n        })} ${multiplier}x`,\n    );\n    return finalSrcs.join(', ');\n  }\n\n  private shouldGenerateAutomaticSrcset(): boolean {\n    let oversizedImage = false;\n    if (!this.sizes) {\n      oversizedImage =\n        this.width! > FIXED_SRCSET_WIDTH_LIMIT || this.height! > FIXED_SRCSET_HEIGHT_LIMIT;\n    }\n    return (\n      !this.disableOptimizedSrcset &&\n      !this.srcset &&\n      this.imageLoader !== noopImageLoader &&\n      !oversizedImage\n    );\n  }\n\n  /**\n   * Returns an image url formatted for use with the CSS background-image property. Expects one of:\n   * * A base64 encoded image, which is wrapped and passed through.\n   * * A boolean. If true, calls the image loader to generate a small placeholder url.\n   */\n  protected generatePlaceholder(placeholderInput: string | boolean): string | boolean | null {\n    const {placeholderResolution} = this.config;\n    if (placeholderInput === true) {\n      return `url(${this.callImageLoader({\n        src: this.ngSrc,\n        width: placeholderResolution,\n        isPlaceholder: true,\n      })})`;\n    } else if (typeof placeholderInput === 'string') {\n      return `url(${placeholderInput})`;\n    }\n    return null;\n  }\n\n  /**\n   * Determines if blur should be applied, based on an optional boolean\n   * property `blur` within the optional configuration object `placeholderConfig`.\n   */\n  protected shouldBlurPlaceholder(placeholderConfig?: ImagePlaceholderConfig): boolean {\n    if (!placeholderConfig || !placeholderConfig.hasOwnProperty('blur')) {\n      return true;\n    }\n    return Boolean(placeholderConfig.blur);\n  }\n\n  private removePlaceholderOnLoad(img: HTMLImageElement): void {\n    const callback = () => {\n      const changeDetectorRef = this.injector.get(ChangeDetectorRef);\n      removeLoadListenerFn();\n      removeErrorListenerFn();\n      this.placeholder = false;\n      changeDetectorRef.markForCheck();\n    };\n\n    const removeLoadListenerFn = this.renderer.listen(img, 'load', callback);\n    const removeErrorListenerFn = this.renderer.listen(img, 'error', callback);\n\n    // Clean up listeners once the view is destroyed, before the image\n    // loads or fails to load, to avoid element from being captured\n    // in memory and redundant change detection.\n    this.destroyRef.onDestroy(() => {\n      removeLoadListenerFn();\n      removeErrorListenerFn();\n    });\n\n    callOnLoadIfImageIsLoaded(img, callback);\n  }\n\n  private setHostAttribute(name: string, value: string): void {\n    this.renderer.setAttribute(this.imgElement, name, value);\n  }\n}\n\n/***** Helpers *****/\n\n/**\n * Sorts provided config breakpoints and uses defaults.\n */\nfunction processConfig(config: ImageConfig): ImageConfig {\n  let sortedBreakpoints: {breakpoints?: number[]} = {};\n  if (config.breakpoints) {\n    sortedBreakpoints.breakpoints = config.breakpoints.sort((a, b) => a - b);\n  }\n  return Object.assign({}, IMAGE_CONFIG_DEFAULTS, config, sortedBreakpoints);\n}\n\n/***** Assert functions *****/\n\n/**\n * Verifies that there is no `src` set on a host element.\n */\nfunction assertNoConflictingSrc(dir: NgOptimizedImage) {\n  if (dir.src) {\n    throw new RuntimeError(\n      RuntimeErrorCode.UNEXPECTED_SRC_ATTR,\n      `${imgDirectiveDetails(dir.ngSrc)} both \\`src\\` and \\`ngSrc\\` have been set. ` +\n        `Supplying both of these attributes breaks lazy loading. ` +\n        `The NgOptimizedImage directive sets \\`src\\` itself based on the value of \\`ngSrc\\`. ` +\n        `To fix this, please remove the \\`src\\` attribute.`,\n    );\n  }\n}\n\n/**\n * Verifies that there is no `srcset` set on a host element.\n */\nfunction assertNoConflictingSrcset(dir: NgOptimizedImage) {\n  if (dir.srcset) {\n    throw new RuntimeError(\n      RuntimeErrorCode.UNEXPECTED_SRCSET_ATTR,\n      `${imgDirectiveDetails(dir.ngSrc)} both \\`srcset\\` and \\`ngSrcset\\` have been set. ` +\n        `Supplying both of these attributes breaks lazy loading. ` +\n        `The NgOptimizedImage directive sets \\`srcset\\` itself based on the value of ` +\n        `\\`ngSrcset\\`. To fix this, please remove the \\`srcset\\` attribute.`,\n    );\n  }\n}\n\n/**\n * Verifies that the `ngSrc` is not a Base64-encoded image.\n */\nfunction assertNotBase64Image(dir: NgOptimizedImage) {\n  let ngSrc = dir.ngSrc.trim();\n  if (ngSrc.startsWith('data:')) {\n    if (ngSrc.length > BASE64_IMG_MAX_LENGTH_IN_ERROR) {\n      ngSrc = ngSrc.substring(0, BASE64_IMG_MAX_LENGTH_IN_ERROR) + '...';\n    }\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc, false)} \\`ngSrc\\` is a Base64-encoded string ` +\n        `(${ngSrc}). NgOptimizedImage does not support Base64-encoded strings. ` +\n        `To fix this, disable the NgOptimizedImage directive for this element ` +\n        `by removing \\`ngSrc\\` and using a standard \\`src\\` attribute instead.`,\n    );\n  }\n}\n\n/**\n * Verifies that the 'sizes' only includes responsive values.\n */\nfunction assertNoComplexSizes(dir: NgOptimizedImage) {\n  let sizes = dir.sizes;\n  if (sizes?.match(/((\\)|,)\\s|^)\\d+px/)) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc, false)} \\`sizes\\` was set to a string including ` +\n        `pixel values. For automatic \\`srcset\\` generation, \\`sizes\\` must only include responsive ` +\n        `values, such as \\`sizes=\"50vw\"\\` or \\`sizes=\"(min-width: 768px) 50vw, 100vw\"\\`. ` +\n        `To fix this, modify the \\`sizes\\` attribute, or provide your own \\`ngSrcset\\` value directly.`,\n    );\n  }\n}\n\nfunction assertValidPlaceholder(dir: NgOptimizedImage, imageLoader: ImageLoader) {\n  assertNoPlaceholderConfigWithoutPlaceholder(dir);\n  assertNoRelativePlaceholderWithoutLoader(dir, imageLoader);\n  assertNoOversizedDataUrl(dir);\n}\n\n/**\n * Verifies that placeholderConfig isn't being used without placeholder\n */\nfunction assertNoPlaceholderConfigWithoutPlaceholder(dir: NgOptimizedImage) {\n  if (dir.placeholderConfig && !dir.placeholder) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(\n        dir.ngSrc,\n        false,\n      )} \\`placeholderConfig\\` options were provided for an ` +\n        `image that does not use the \\`placeholder\\` attribute, and will have no effect.`,\n    );\n  }\n}\n\n/**\n * Warns if a relative URL placeholder is specified, but no loader is present to provide the small\n * image.\n */\nfunction assertNoRelativePlaceholderWithoutLoader(dir: NgOptimizedImage, imageLoader: ImageLoader) {\n  if (dir.placeholder === true && imageLoader === noopImageLoader) {\n    throw new RuntimeError(\n      RuntimeErrorCode.MISSING_NECESSARY_LOADER,\n      `${imgDirectiveDetails(dir.ngSrc)} the \\`placeholder\\` attribute is set to true but ` +\n        `no image loader is configured (i.e. the default one is being used), ` +\n        `which would result in the same image being used for the primary image and its placeholder. ` +\n        `To fix this, provide a loader or remove the \\`placeholder\\` attribute from the image.`,\n    );\n  }\n}\n\n/**\n * Warns or throws an error if an oversized dataURL placeholder is provided.\n */\nfunction assertNoOversizedDataUrl(dir: NgOptimizedImage) {\n  if (\n    dir.placeholder &&\n    typeof dir.placeholder === 'string' &&\n    dir.placeholder.startsWith('data:')\n  ) {\n    if (dir.placeholder.length > DATA_URL_ERROR_LIMIT) {\n      throw new RuntimeError(\n        RuntimeErrorCode.OVERSIZED_PLACEHOLDER,\n        `${imgDirectiveDetails(\n          dir.ngSrc,\n        )} the \\`placeholder\\` attribute is set to a data URL which is longer ` +\n          `than ${DATA_URL_ERROR_LIMIT} characters. This is strongly discouraged, as large inline placeholders ` +\n          `directly increase the bundle size of Angular and hurt page load performance. To fix this, generate ` +\n          `a smaller data URL placeholder.`,\n      );\n    }\n    if (dir.placeholder.length > DATA_URL_WARN_LIMIT) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.OVERSIZED_PLACEHOLDER,\n          `${imgDirectiveDetails(\n            dir.ngSrc,\n          )} the \\`placeholder\\` attribute is set to a data URL which is longer ` +\n            `than ${DATA_URL_WARN_LIMIT} characters. This is discouraged, as large inline placeholders ` +\n            `directly increase the bundle size of Angular and hurt page load performance. For better loading performance, ` +\n            `generate a smaller data URL placeholder.`,\n        ),\n      );\n    }\n  }\n}\n\n/**\n * Verifies that the `ngSrc` is not a Blob URL.\n */\nfunction assertNotBlobUrl(dir: NgOptimizedImage) {\n  const ngSrc = dir.ngSrc.trim();\n  if (ngSrc.startsWith('blob:')) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} \\`ngSrc\\` was set to a blob URL (${ngSrc}). ` +\n        `Blob URLs are not supported by the NgOptimizedImage directive. ` +\n        `To fix this, disable the NgOptimizedImage directive for this element ` +\n        `by removing \\`ngSrc\\` and using a regular \\`src\\` attribute instead.`,\n    );\n  }\n}\n\n/**\n * Verifies that the input is set to a non-empty string.\n */\nfunction assertNonEmptyInput(dir: NgOptimizedImage, name: string, value: unknown) {\n  const isString = typeof value === 'string';\n  const isEmptyString = isString && value.trim() === '';\n  if (!isString || isEmptyString) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} \\`${name}\\` has an invalid value ` +\n        `(\\`${value}\\`). To fix this, change the value to a non-empty string.`,\n    );\n  }\n}\n\n/**\n * Verifies that the `ngSrcset` is in a valid format, e.g. \"100w, 200w\" or \"1x, 2x\".\n */\nexport function assertValidNgSrcset(dir: NgOptimizedImage, value: unknown) {\n  if (value == null) return;\n  assertNonEmptyInput(dir, 'ngSrcset', value);\n  const stringVal = value as string;\n  const isValidWidthDescriptor = VALID_WIDTH_DESCRIPTOR_SRCSET.test(stringVal);\n  const isValidDensityDescriptor = VALID_DENSITY_DESCRIPTOR_SRCSET.test(stringVal);\n\n  if (isValidDensityDescriptor) {\n    assertUnderDensityCap(dir, stringVal);\n  }\n\n  const isValidSrcset = isValidWidthDescriptor || isValidDensityDescriptor;\n  if (!isValidSrcset) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} \\`ngSrcset\\` has an invalid value (\\`${value}\\`). ` +\n        `To fix this, supply \\`ngSrcset\\` using a comma-separated list of one or more width ` +\n        `descriptors (e.g. \"100w, 200w\") or density descriptors (e.g. \"1x, 2x\").`,\n    );\n  }\n}\n\nfunction assertUnderDensityCap(dir: NgOptimizedImage, value: string) {\n  const underDensityCap = value\n    .split(',')\n    .every((num) => num === '' || parseFloat(num) <= ABSOLUTE_SRCSET_DENSITY_CAP);\n  if (!underDensityCap) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} the \\`ngSrcset\\` contains an unsupported image density:` +\n        `\\`${value}\\`. NgOptimizedImage generally recommends a max image density of ` +\n        `${RECOMMENDED_SRCSET_DENSITY_CAP}x but supports image densities up to ` +\n        `${ABSOLUTE_SRCSET_DENSITY_CAP}x. The human eye cannot distinguish between image densities ` +\n        `greater than ${RECOMMENDED_SRCSET_DENSITY_CAP}x - which makes them unnecessary for ` +\n        `most use cases. Images that will be pinch-zoomed are typically the primary use case for ` +\n        `${ABSOLUTE_SRCSET_DENSITY_CAP}x images. Please remove the high density descriptor and try again.`,\n    );\n  }\n}\n\n/**\n * Creates a `RuntimeError` instance to represent a situation when an input is set after\n * the directive has initialized.\n */\nfunction postInitInputChangeError(dir: NgOptimizedImage, inputName: string): {} {\n  let reason!: string;\n  if (inputName === 'width' || inputName === 'height') {\n    reason =\n      `Changing \\`${inputName}\\` may result in different attribute value ` +\n      `applied to the underlying image element and cause layout shifts on a page.`;\n  } else {\n    reason =\n      `Changing the \\`${inputName}\\` would have no effect on the underlying ` +\n      `image element, because the resource loading has already occurred.`;\n  }\n  return new RuntimeError(\n    RuntimeErrorCode.UNEXPECTED_INPUT_CHANGE,\n    `${imgDirectiveDetails(dir.ngSrc)} \\`${inputName}\\` was updated after initialization. ` +\n      `The NgOptimizedImage directive will not react to this input change. ${reason} ` +\n      `To fix this, either switch \\`${inputName}\\` to a static value ` +\n      `or wrap the image element in an @if that is gated on the necessary value.`,\n  );\n}\n\n/**\n * Verify that none of the listed inputs has changed.\n */\nfunction assertNoPostInitInputChange(\n  dir: NgOptimizedImage,\n  changes: SimpleChanges,\n  inputs: string[],\n) {\n  inputs.forEach((input) => {\n    const isUpdated = changes.hasOwnProperty(input);\n    if (isUpdated && !changes[input].isFirstChange()) {\n      if (input === 'ngSrc') {\n        // When the `ngSrc` input changes, we detect that only in the\n        // `ngOnChanges` hook, thus the `ngSrc` is already set. We use\n        // `ngSrc` in the error message, so we use a previous value, but\n        // not the updated one in it.\n        dir = {ngSrc: changes[input].previousValue} as NgOptimizedImage;\n      }\n      throw postInitInputChangeError(dir, input);\n    }\n  });\n}\n\n/**\n * Verifies that a specified input is a number greater than 0.\n */\nfunction assertGreaterThanZero(dir: NgOptimizedImage, inputValue: unknown, inputName: string) {\n  const validNumber = typeof inputValue === 'number' && inputValue > 0;\n  const validString =\n    typeof inputValue === 'string' && /^\\d+$/.test(inputValue.trim()) && parseInt(inputValue) > 0;\n  if (!validNumber && !validString) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} \\`${inputName}\\` has an invalid value. ` +\n        `To fix this, provide \\`${inputName}\\` as a number greater than 0.`,\n    );\n  }\n}\n\n/**\n * Verifies that the rendered image is not visually distorted. Effectively this is checking:\n * - Whether the \"width\" and \"height\" attributes reflect the actual dimensions of the image.\n * - Whether image styling is \"correct\" (see below for a longer explanation).\n */\nfunction assertNoImageDistortion(\n  dir: NgOptimizedImage,\n  img: HTMLImageElement,\n  renderer: Renderer2,\n  destroyRef: DestroyRef,\n) {\n  const callback = () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n    const computedStyle = window.getComputedStyle(img);\n    let renderedWidth = parseFloat(computedStyle.getPropertyValue('width'));\n    let renderedHeight = parseFloat(computedStyle.getPropertyValue('height'));\n    const boxSizing = computedStyle.getPropertyValue('box-sizing');\n\n    if (boxSizing === 'border-box') {\n      const paddingTop = computedStyle.getPropertyValue('padding-top');\n      const paddingRight = computedStyle.getPropertyValue('padding-right');\n      const paddingBottom = computedStyle.getPropertyValue('padding-bottom');\n      const paddingLeft = computedStyle.getPropertyValue('padding-left');\n      renderedWidth -= parseFloat(paddingRight) + parseFloat(paddingLeft);\n      renderedHeight -= parseFloat(paddingTop) + parseFloat(paddingBottom);\n    }\n\n    const renderedAspectRatio = renderedWidth / renderedHeight;\n    const nonZeroRenderedDimensions = renderedWidth !== 0 && renderedHeight !== 0;\n\n    const intrinsicWidth = img.naturalWidth;\n    const intrinsicHeight = img.naturalHeight;\n    const intrinsicAspectRatio = intrinsicWidth / intrinsicHeight;\n\n    const suppliedWidth = dir.width!;\n    const suppliedHeight = dir.height!;\n    const suppliedAspectRatio = suppliedWidth / suppliedHeight;\n\n    // Tolerance is used to account for the impact of subpixel rendering.\n    // Due to subpixel rendering, the rendered, intrinsic, and supplied\n    // aspect ratios of a correctly configured image may not exactly match.\n    // For example, a `width=4030 height=3020` image might have a rendered\n    // size of \"1062w, 796.48h\". (An aspect ratio of 1.334... vs. 1.333...)\n    const inaccurateDimensions =\n      Math.abs(suppliedAspectRatio - intrinsicAspectRatio) > ASPECT_RATIO_TOLERANCE;\n    const stylingDistortion =\n      nonZeroRenderedDimensions &&\n      Math.abs(intrinsicAspectRatio - renderedAspectRatio) > ASPECT_RATIO_TOLERANCE;\n\n    if (inaccurateDimensions) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.INVALID_INPUT,\n          `${imgDirectiveDetails(dir.ngSrc)} the aspect ratio of the image does not match ` +\n            `the aspect ratio indicated by the width and height attributes. ` +\n            `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h ` +\n            `(aspect-ratio: ${round(\n              intrinsicAspectRatio,\n            )}). \\nSupplied width and height attributes: ` +\n            `${suppliedWidth}w x ${suppliedHeight}h (aspect-ratio: ${round(\n              suppliedAspectRatio,\n            )}). ` +\n            `\\nTo fix this, update the width and height attributes.`,\n        ),\n      );\n    } else if (stylingDistortion) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.INVALID_INPUT,\n          `${imgDirectiveDetails(dir.ngSrc)} the aspect ratio of the rendered image ` +\n            `does not match the image's intrinsic aspect ratio. ` +\n            `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h ` +\n            `(aspect-ratio: ${round(intrinsicAspectRatio)}). \\nRendered image size: ` +\n            `${renderedWidth}w x ${renderedHeight}h (aspect-ratio: ` +\n            `${round(renderedAspectRatio)}). \\nThis issue can occur if \"width\" and \"height\" ` +\n            `attributes are added to an image without updating the corresponding ` +\n            `image styling. To fix this, adjust image styling. In most cases, ` +\n            `adding \"height: auto\" or \"width: auto\" to the image styling will fix ` +\n            `this issue.`,\n        ),\n      );\n    } else if (!dir.ngSrcset && nonZeroRenderedDimensions) {\n      // If `ngSrcset` hasn't been set, sanity check the intrinsic size.\n      const recommendedWidth = RECOMMENDED_SRCSET_DENSITY_CAP * renderedWidth;\n      const recommendedHeight = RECOMMENDED_SRCSET_DENSITY_CAP * renderedHeight;\n      const oversizedWidth = intrinsicWidth - recommendedWidth >= OVERSIZED_IMAGE_TOLERANCE;\n      const oversizedHeight = intrinsicHeight - recommendedHeight >= OVERSIZED_IMAGE_TOLERANCE;\n      if (oversizedWidth || oversizedHeight) {\n        console.warn(\n          formatRuntimeError(\n            RuntimeErrorCode.OVERSIZED_IMAGE,\n            `${imgDirectiveDetails(dir.ngSrc)} the intrinsic image is significantly ` +\n              `larger than necessary. ` +\n              `\\nRendered image size: ${renderedWidth}w x ${renderedHeight}h. ` +\n              `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h. ` +\n              `\\nRecommended intrinsic image size: ${recommendedWidth}w x ${recommendedHeight}h. ` +\n              `\\nNote: Recommended intrinsic image size is calculated assuming a maximum DPR of ` +\n              `${RECOMMENDED_SRCSET_DENSITY_CAP}. To improve loading time, resize the image ` +\n              `or consider using the \"ngSrcset\" and \"sizes\" attributes.`,\n          ),\n        );\n      }\n    }\n  };\n\n  const removeLoadListenerFn = renderer.listen(img, 'load', callback);\n\n  // We only listen to the `error` event to remove the `load` event listener because it will not be\n  // fired if the image fails to load. This is done to prevent memory leaks in development mode\n  // because image elements aren't garbage-collected properly. It happens because zone.js stores the\n  // event listener directly on the element and closures capture `dir`.\n  const removeErrorListenerFn = renderer.listen(img, 'error', () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n  });\n\n  // Clean up listeners once the view is destroyed, before the image\n  // loads or fails to load, to avoid element from being captured\n  // in memory and redundant change detection.\n  destroyRef.onDestroy(() => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n  });\n\n  callOnLoadIfImageIsLoaded(img, callback);\n}\n\n/**\n * Verifies that a specified input is set.\n */\nfunction assertNonEmptyWidthAndHeight(dir: NgOptimizedImage) {\n  let missingAttributes = [];\n  if (dir.width === undefined) missingAttributes.push('width');\n  if (dir.height === undefined) missingAttributes.push('height');\n  if (missingAttributes.length > 0) {\n    throw new RuntimeError(\n      RuntimeErrorCode.REQUIRED_INPUT_MISSING,\n      `${imgDirectiveDetails(dir.ngSrc)} these required attributes ` +\n        `are missing: ${missingAttributes.map((attr) => `\"${attr}\"`).join(', ')}. ` +\n        `Including \"width\" and \"height\" attributes will prevent image-related layout shifts. ` +\n        `To fix this, include \"width\" and \"height\" attributes on the image tag or turn on ` +\n        `\"fill\" mode with the \\`fill\\` attribute.`,\n    );\n  }\n}\n\n/**\n * Verifies that width and height are not set. Used in fill mode, where those attributes don't make\n * sense.\n */\nfunction assertEmptyWidthAndHeight(dir: NgOptimizedImage) {\n  if (dir.width || dir.height) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} the attributes \\`height\\` and/or \\`width\\` are present ` +\n        `along with the \\`fill\\` attribute. Because \\`fill\\` mode causes an image to fill its containing ` +\n        `element, the size attributes have no effect and should be removed.`,\n    );\n  }\n}\n\n/**\n * Verifies that the rendered image has a nonzero height. If the image is in fill mode, provides\n * guidance that this can be caused by the containing element's CSS position property.\n */\nfunction assertNonZeroRenderedHeight(\n  dir: NgOptimizedImage,\n  img: HTMLImageElement,\n  renderer: Renderer2,\n  destroyRef: DestroyRef,\n) {\n  const callback = () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n    const renderedHeight = img.clientHeight;\n    if (dir.fill && renderedHeight === 0) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.INVALID_INPUT,\n          `${imgDirectiveDetails(dir.ngSrc)} the height of the fill-mode image is zero. ` +\n            `This is likely because the containing element does not have the CSS 'position' ` +\n            `property set to one of the following: \"relative\", \"fixed\", or \"absolute\". ` +\n            `To fix this problem, make sure the container element has the CSS 'position' ` +\n            `property defined and the height of the element is not zero.`,\n        ),\n      );\n    }\n  };\n\n  const removeLoadListenerFn = renderer.listen(img, 'load', callback);\n\n  // See comments in the `assertNoImageDistortion`.\n  const removeErrorListenerFn = renderer.listen(img, 'error', () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n  });\n\n  // Clean up listeners once the view is destroyed, before the image\n  // loads or fails to load, to avoid element from being captured\n  // in memory and redundant change detection.\n  destroyRef.onDestroy(() => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n  });\n\n  callOnLoadIfImageIsLoaded(img, callback);\n}\n\n/**\n * Verifies that the `loading` attribute is set to a valid input &\n * is not used on priority images.\n */\nfunction assertValidLoadingInput(dir: NgOptimizedImage) {\n  if (dir.loading && dir.priority) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} the \\`loading\\` attribute ` +\n        `was used on an image that was marked \"priority\". ` +\n        `Setting \\`loading\\` on priority images is not allowed ` +\n        `because these images will always be eagerly loaded. ` +\n        `To fix this, remove the “loading” attribute from the priority image.`,\n    );\n  }\n  const validInputs = ['auto', 'eager', 'lazy'];\n  if (typeof dir.loading === 'string' && !validInputs.includes(dir.loading)) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} the \\`loading\\` attribute ` +\n        `has an invalid value (\\`${dir.loading}\\`). ` +\n        `To fix this, provide a valid value (\"lazy\", \"eager\", or \"auto\").`,\n    );\n  }\n}\n\n/**\n * Verifies that the `decoding` attribute is set to a valid input.\n */\nfunction assertValidDecodingInput(dir: NgOptimizedImage) {\n  const validInputs = ['sync', 'async', 'auto'];\n  if (typeof dir.decoding === 'string' && !validInputs.includes(dir.decoding)) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INPUT,\n      `${imgDirectiveDetails(dir.ngSrc)} the \\`decoding\\` attribute ` +\n        `has an invalid value (\\`${dir.decoding}\\`). ` +\n        `To fix this, provide a valid value (\"sync\", \"async\", or \"auto\").`,\n    );\n  }\n}\n\n/**\n * Warns if NOT using a loader (falling back to the generic loader) and\n * the image appears to be hosted on one of the image CDNs for which\n * we do have a built-in image loader. Suggests switching to the\n * built-in loader.\n *\n * @param ngSrc Value of the ngSrc attribute\n * @param imageLoader ImageLoader provided\n */\nfunction assertNotMissingBuiltInLoader(ngSrc: string, imageLoader: ImageLoader) {\n  if (imageLoader === noopImageLoader) {\n    let builtInLoaderName = '';\n    for (const loader of BUILT_IN_LOADERS) {\n      if (loader.testUrl(ngSrc)) {\n        builtInLoaderName = loader.name;\n        break;\n      }\n    }\n    if (builtInLoaderName) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.MISSING_BUILTIN_LOADER,\n          `NgOptimizedImage: It looks like your images may be hosted on the ` +\n            `${builtInLoaderName} CDN, but your app is not using Angular's ` +\n            `built-in loader for that CDN. We recommend switching to use ` +\n            `the built-in by calling \\`provide${builtInLoaderName}Loader()\\` ` +\n            `in your \\`providers\\` and passing it your instance's base URL. ` +\n            `If you don't want to use the built-in loader, define a custom ` +\n            `loader function using IMAGE_LOADER to silence this warning.`,\n        ),\n      );\n    }\n  }\n}\n\n/**\n * Warns if ngSrcset is present and no loader is configured (i.e. the default one is being used).\n */\nfunction assertNoNgSrcsetWithoutLoader(dir: NgOptimizedImage, imageLoader: ImageLoader) {\n  if (dir.ngSrcset && imageLoader === noopImageLoader) {\n    console.warn(\n      formatRuntimeError(\n        RuntimeErrorCode.MISSING_NECESSARY_LOADER,\n        `${imgDirectiveDetails(dir.ngSrc)} the \\`ngSrcset\\` attribute is present but ` +\n          `no image loader is configured (i.e. the default one is being used), ` +\n          `which would result in the same image being used for all configured sizes. ` +\n          `To fix this, provide a loader or remove the \\`ngSrcset\\` attribute from the image.`,\n      ),\n    );\n  }\n}\n\n/**\n * Warns if loaderParams is present and no loader is configured (i.e. the default one is being\n * used).\n */\nfunction assertNoLoaderParamsWithoutLoader(dir: NgOptimizedImage, imageLoader: ImageLoader) {\n  if (dir.loaderParams && imageLoader === noopImageLoader) {\n    console.warn(\n      formatRuntimeError(\n        RuntimeErrorCode.MISSING_NECESSARY_LOADER,\n        `${imgDirectiveDetails(dir.ngSrc)} the \\`loaderParams\\` attribute is present but ` +\n          `no image loader is configured (i.e. the default one is being used), ` +\n          `which means that the loaderParams data will not be consumed and will not affect the URL. ` +\n          `To fix this, provide a custom loader or remove the \\`loaderParams\\` attribute from the image.`,\n      ),\n    );\n  }\n}\n\n/**\n * Warns if the priority attribute is used too often on page load\n */\nasync function assetPriorityCountBelowThreshold(appRef: ApplicationRef) {\n  if (IMGS_WITH_PRIORITY_ATTR_COUNT === 0) {\n    IMGS_WITH_PRIORITY_ATTR_COUNT++;\n    await appRef.whenStable();\n    if (IMGS_WITH_PRIORITY_ATTR_COUNT > PRIORITY_COUNT_THRESHOLD) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.TOO_MANY_PRIORITY_ATTRIBUTES,\n          `NgOptimizedImage: The \"priority\" attribute is set to true more than ${PRIORITY_COUNT_THRESHOLD} times (${IMGS_WITH_PRIORITY_ATTR_COUNT} times). ` +\n            `Marking too many images as \"high\" priority can hurt your application's LCP (https://web.dev/lcp). ` +\n            `\"Priority\" should only be set on the image expected to be the page's LCP element.`,\n        ),\n      );\n    }\n  } else {\n    IMGS_WITH_PRIORITY_ATTR_COUNT++;\n  }\n}\n\n/**\n * Warns if placeholder's dimension are over a threshold.\n *\n * This assert function is meant to only run on the browser.\n */\nfunction assertPlaceholderDimensions(dir: NgOptimizedImage, imgElement: HTMLImageElement) {\n  const computedStyle = window.getComputedStyle(imgElement);\n  let renderedWidth = parseFloat(computedStyle.getPropertyValue('width'));\n  let renderedHeight = parseFloat(computedStyle.getPropertyValue('height'));\n\n  if (renderedWidth > PLACEHOLDER_DIMENSION_LIMIT || renderedHeight > PLACEHOLDER_DIMENSION_LIMIT) {\n    console.warn(\n      formatRuntimeError(\n        RuntimeErrorCode.PLACEHOLDER_DIMENSION_LIMIT_EXCEEDED,\n        `${imgDirectiveDetails(dir.ngSrc)} it uses a placeholder image, but at least one ` +\n          `of the dimensions attribute (height or width) exceeds the limit of ${PLACEHOLDER_DIMENSION_LIMIT}px. ` +\n          `To fix this, use a smaller image as a placeholder.`,\n      ),\n    );\n  }\n}\n\nfunction callOnLoadIfImageIsLoaded(img: HTMLImageElement, callback: VoidFunction): void {\n  // https://html.spec.whatwg.org/multipage/embedded-content.html#dom-img-complete\n  // The spec defines that `complete` is truthy once its request state is fully available.\n  // The image may already be available if it’s loaded from the browser cache.\n  // In that case, the `load` event will not fire at all, meaning that all setup\n  // callbacks listening for the `load` event will not be invoked.\n  // In Safari, there is a known behavior where the `complete` property of an\n  // `HTMLImageElement` may sometimes return `true` even when the image is not fully loaded.\n  // Checking both `img.complete` and `img.naturalWidth` is the most reliable way to\n  // determine if an image has been fully loaded, especially in browsers where the\n  // `complete` property may return `true` prematurely.\n  if (img.complete && img.naturalWidth) {\n    callback();\n  }\n}\n\nfunction round(input: number): number | string {\n  return Number.isInteger(input) ? input : input.toFixed(2);\n}\n\n// Transform function to handle SafeValue input for ngSrc. This doesn't do any sanitization,\n// as that is not needed for img.src and img.srcset. This transform is purely for compatibility.\nfunction unwrapSafeUrl(value: string | SafeValue): string {\n  if (typeof value === 'string') {\n    return value;\n  }\n  return unwrapSafeValue(value);\n}\n\n// Transform function to handle inputs which may be booleans, strings, or string representations\n// of boolean values. Used for the placeholder attribute.\nexport function booleanOrUrlAttribute(value: boolean | string): boolean | string {\n  if (typeof value === 'string' && value !== 'true' && value !== 'false' && value !== '') {\n    return value;\n  }\n  return booleanAttribute(value);\n}\n"], "names": ["ɵregisterLocaleData", "ɵɵdefineInjectable", "formatRuntimeError", "RuntimeError", "IMAGE_CONFIG", "performanceMarkFeature", "IMAGE_CONFIG_DEFAULTS", "unwrapSafeValue"], "mappings": ";;;;;;;;;;;;;;;AAUA;;;;;;;;AAQG;SACa,kBAAkB,CAAC,IAAS,EAAE,QAAuB,EAAE,SAAe,EAAA;IACpF,OAAOA,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,SAAS,CAAC;AACvD;;ACbO,MAAM,mBAAmB,GAAG;AAC5B,MAAM,kBAAkB,GAAG;AAElC;;;AAGG;AACG,SAAU,iBAAiB,CAAC,UAAkB,EAAA;IAClD,OAAO,UAAU,KAAK,mBAAmB;AAC3C;AAEA;;;AAGG;AACG,SAAU,gBAAgB,CAAC,UAAkB,EAAA;IACjD,OAAO,UAAU,KAAK,kBAAkB;AAC1C;;ACjBA;;;;AAIG;AAIH;;AAEG;AACU,MAAA,OAAO,mBAAmB,IAAI,OAAO,CAAC,mBAAmB;;ACHtE;;;;AAIG;MACmB,gBAAgB,CAAA;;;;IAIpC,OAAO,KAAK,6CAA6CC,kBAAkB,CAAC;AAC1E,QAAA,KAAK,EAAE,gBAAgB;AACvB,QAAA,UAAU,EAAE,MAAM;QAClB,OAAO,EAAE,MACP,OAAO,YAAY,KAAK,WAAW,IAAI;cACnC,IAAI,oBAAoB;cACxB,IAAI,uBAAuB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;AAC5D,KAAA,CAAC;;AAoCJ;;AAEG;MACU,uBAAuB,CAAA;AAIxB,IAAA,QAAA;AACA,IAAA,MAAA;IAJF,MAAM,GAA2B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAErD,WACU,CAAA,QAAkB,EAClB,MAAc,EAAA;QADd,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAM,CAAA,MAAA,GAAN,MAAM;;AAGhB;;;;;AAKG;AACH,IAAA,SAAS,CAAC,MAAmD,EAAA;AAC3D,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACzB,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;;aACrB;AACL,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM;;;AAIxB;;;AAGG;IACH,iBAAiB,GAAA;AACf,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;;AAGnD;;;AAGG;IACH,gBAAgB,CAAC,QAA0B,EAAE,OAAuB,EAAA;QAClE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAC,GAAG,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAC,CAAC;;AAGzE;;;;;;;;;;AAUG;IACH,cAAc,CAAC,MAAc,EAAE,OAAuB,EAAA;QACpD,MAAM,UAAU,GAAG,sBAAsB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC;QAEhE,IAAI,UAAU,EAAE;AACd,YAAA,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC;;;;;;;YAOzC,UAAU,CAAC,KAAK,EAAE;;;AAItB;;AAEG;AACH,IAAA,2BAA2B,CAAC,iBAAoC,EAAA;AAC9D,QAAA,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iBAAiB,GAAG,iBAAiB;;AACzD,QAAA,MAAM;AACN,YAAA,OAAO,CAAC,IAAI,CACVC,mBAAkB,6DAEhB,SAAS;gBACP,oDAAoD;oBAClD,wBAAwB;oBACxB,qDAAqD;oBACrD,mDAAmD;oBACnD,6HAA6H;oBAC7H,yDAAyD,CAC9D,CACF;;;AAIL;;;;;AAKG;IACK,eAAe,CAAC,EAAe,EAAE,OAAuB,EAAA;AAC9D,QAAA,MAAM,IAAI,GAAG,EAAE,CAAC,qBAAqB,EAAE;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;QAChD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;AAC9C,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;AAC5B,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;AACnB,YAAA,GAAG,OAAO;AACV,YAAA,IAAI,EAAE,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;AACtB,YAAA,GAAG,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC;AACrB,SAAA,CAAC;;AAEL;AAED,SAAS,sBAAsB,CAAC,QAAkB,EAAE,MAAc,EAAA;AAChE,IAAA,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAE/F,IAAI,cAAc,EAAE;AAClB,QAAA,OAAO,cAAc;;;;AAKvB,IAAA,IACE,OAAO,QAAQ,CAAC,gBAAgB,KAAK,UAAU;AAC/C,QAAA,QAAQ,CAAC,IAAI;QACb,OAAO,QAAQ,CAAC,IAAI,CAAC,YAAY,KAAK,UAAU,EAChD;AACA,QAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC;AACpF,QAAA,IAAI,WAAW,GAAG,UAAU,CAAC,WAAiC;QAE9D,OAAO,WAAW,EAAE;AAClB,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU;YAEzC,IAAI,UAAU,EAAE;;;AAGd,gBAAA,MAAM,MAAM,GACV,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,aAAa,CAAC,UAAU,MAAM,CAAA,EAAA,CAAI,CAAC;gBACrF,IAAI,MAAM,EAAE;AACV,oBAAA,OAAO,MAAM;;;AAIjB,YAAA,WAAW,GAAG,UAAU,CAAC,QAAQ,EAAwB;;;AAI7D,IAAA,OAAO,IAAI;AACb;AAEA;;AAEG;MACU,oBAAoB,CAAA;AAC/B;;AAEG;IACH,SAAS,CAAC,MAAmD,EAAA;AAE7D;;AAEG;IACH,iBAAiB,GAAA;AACf,QAAA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;;AAGf;;AAEG;IACH,gBAAgB,CAAC,QAA0B,EAAA;AAE3C;;AAEG;IACH,cAAc,CAAC,MAAc,EAAA;AAE7B;;AAEG;IACH,2BAA2B,CAAC,iBAAoC,EAAA;AACjE;;AC3OD;;AAEG;AACI,MAAM,mBAAmB,GAAG,IAAI;;ACHvC;AACgB,SAAA,MAAM,CAAC,GAAW,EAAE,GAAW,EAAA;;IAE7C,OAAO,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC5E;AAEA;AACM,SAAU,aAAa,CAAC,GAAW,EAAA;AACvC,IAAA,OAAO,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC;AACjC;AAEA;AACA;AACM,SAAU,eAAe,CAAC,GAAW,EAAA;AACzC,IAAA,OAAO,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,GAAG;AACzD;AAEM,SAAU,WAAW,CAAC,IAAa,EAAA;AACvC,IAAA,MAAM,QAAQ,GAAG,OAAO,IAAI,KAAK,QAAQ;IAEzC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;AACnC,QAAA,OAAO,KAAK;;;AAId,IAAA,IAAI;AACF,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC;AACzB,QAAA,OAAO,IAAI;;AACX,IAAA,MAAM;AACN,QAAA,OAAO,KAAK;;AAEhB;AAEM,SAAU,aAAa,CAAC,IAAY,EAAA;IACxC,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI;AACtD;AAEM,SAAU,YAAY,CAAC,GAAW,EAAA;AACtC,IAAA,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG;AACjD;;ACCA;;;;;;AAMG;AACI,MAAM,eAAe,GAAG,CAAC,MAAyB,KAAK,MAAM,CAAC,GAAG;AAUxE;;;;;;AAMG;AACU,MAAA,YAAY,GAAG,IAAI,cAAc,CAAc,SAAS,GAAG,aAAa,GAAG,EAAE,EAAE;AAC1F,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,MAAM,eAAe;AAC/B,CAAA;AAED;;;;;;;;AAQG;AACa,SAAA,iBAAiB,CAC/B,UAA+D,EAC/D,WAAsB,EAAA;IAEtB,OAAO,SAAS,kBAAkB,CAAC,IAAY,EAAA;AAC7C,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;AACtB,YAAA,qBAAqB,CAAC,IAAI,EAAE,WAAW,IAAI,EAAE,CAAC;;;;AAKhD,QAAA,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;AAE1B,QAAA,MAAM,QAAQ,GAAG,CAAC,MAAyB,KAAI;AAC7C,YAAA,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;;;;;;AAM7B,gBAAA,+BAA+B,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC;;AAGnD,YAAA,OAAO,UAAU,CAAC,IAAI,EAAE,EAAC,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC,CAAC;AACrE,SAAC;AAED,QAAA,MAAM,SAAS,GAAe,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC;AAC3E,QAAA,OAAO,SAAS;AAClB,KAAC;AACH;AAEA,SAAS,qBAAqB,CAAC,IAAa,EAAE,WAAqB,EAAA;IACjE,MAAM,IAAIC,aAAY,CAAA,IAAA,kDAEpB,SAAS;AACP,QAAA,CAAA,6CAAA,EAAgD,IAAI,CAAO,KAAA,CAAA;YACzD,CAAkE,+DAAA,EAAA,WAAW,CAAC,IAAI,CAChF,MAAM,CACP,CAAA,CAAE,CACR;AACH;AAEA,SAAS,+BAA+B,CAAC,IAAY,EAAE,GAAW,EAAA;IAChE,MAAM,IAAIA,aAAY,CAAA,IAAA,kDAEpB,SAAS;AACP,QAAA,CAAA,+EAAA,EAAkF,GAAG,CAAI,EAAA,CAAA;YACvF,CAA6D,2DAAA,CAAA;YAC7D,CAAiD,+CAAA,CAAA;YACjD,CAAoE,kEAAA,CAAA;YACpE,CAAiC,8BAAA,EAAA,IAAI,CAAM,IAAA,CAAA,CAChD;AACH;;AC9HA;;;;;;;;;;AAUG;MACU,uBAAuB,GAAiC,iBAAiB,CACpF,mBAAmB,EACnB,SAAS,GAAG,CAAC,uDAAuD,CAAC,GAAG,SAAS;AAGnF,SAAS,mBAAmB,CAAC,IAAY,EAAE,MAAyB,EAAA;IAClE,IAAI,MAAM,GAAG,CAAA,WAAA,CAAa;AAC1B,IAAA,IAAI,MAAM,CAAC,KAAK,EAAE;AAChB,QAAA,MAAM,IAAI,CAAU,OAAA,EAAA,MAAM,CAAC,KAAK,EAAE;;;AAIpC,IAAA,IAAI,MAAM,CAAC,aAAa,EAAE;AACxB,QAAA,MAAM,IAAI,CAAA,SAAA,EAAY,mBAAmB,CAAA,CAAE;;;;IAK7C,OAAO,CAAA,EAAG,IAAI,CAAkB,eAAA,EAAA,MAAM,IAAI,MAAM,CAAC,GAAG,CAAA,CAAE;AACxD;;AC/BA;;AAEG;AACI,MAAM,oBAAoB,GAAoB;AACnD,IAAA,IAAI,EAAE,YAAY;AAClB,IAAA,OAAO,EAAE,eAAe;CACzB;AAED,MAAM,uBAAuB,GAAG,yCAAyC;AACzE;;AAEG;AACH,SAAS,eAAe,CAAC,GAAW,EAAA;AAClC,IAAA,OAAO,uBAAuB,CAAC,IAAI,CAAC,GAAG,CAAC;AAC1C;AAEA;;;;;;;;;;;AAWG;MACU,uBAAuB,GAAiC,iBAAiB,CACpF,mBAAmB,EACnB;AACE,MAAE;QACE,mCAAmC;QACnC,+BAA+B;QAC/B,8BAA8B;AAC/B;MACD,SAAS;AAGf,SAAS,mBAAmB,CAAC,IAAY,EAAE,MAAyB,EAAA;;;;;;;AAQlE,IAAA,MAAM,OAAO,GAAG,MAAM,CAAC,aAAa,GAAG,YAAY,GAAG,QAAQ;AAE9D,IAAA,IAAI,MAAM,GAAG,CAAU,OAAA,EAAA,OAAO,EAAE;AAChC,IAAA,IAAI,MAAM,CAAC,KAAK,EAAE;AAChB,QAAA,MAAM,IAAI,CAAM,GAAA,EAAA,MAAM,CAAC,KAAK,EAAE;;IAGhC,IAAI,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC,EAAE;QACpC,MAAM,IAAI,QAAQ;;IAGpB,OAAO,CAAA,EAAG,IAAI,CAAiB,cAAA,EAAA,MAAM,IAAI,MAAM,CAAC,GAAG,CAAA,CAAE;AACvD;;AC1DA;;AAEG;AACI,MAAM,kBAAkB,GAAoB;AACjD,IAAA,IAAI,EAAE,UAAU;AAChB,IAAA,OAAO,EAAE,aAAa;CACvB;AAED,MAAM,sBAAsB,GAAG,sCAAsC;AACrE;;AAEG;AACH,SAAS,aAAa,CAAC,GAAW,EAAA;AAChC,IAAA,OAAO,sBAAsB,CAAC,IAAI,CAAC,GAAG,CAAC;AACzC;AAEA;;;;;;;;;;AAUG;MACU,qBAAqB,GAAiC,iBAAiB,CAClF,iBAAiB,EACjB,SAAS,GAAG,CAAC,+BAA+B,EAAE,8BAA8B,CAAC,GAAG,SAAS;AAG3E,SAAA,iBAAiB,CAAC,IAAY,EAAE,MAAyB,EAAA;;;AAGvE,IAAA,MAAM,EAAC,GAAG,EAAE,KAAK,EAAC,GAAG,MAAM;IAC3B,MAAM,MAAM,GAAa,EAAE;IAE3B,IAAI,KAAK,EAAE;AACT,QAAA,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,CAAA,CAAE,CAAC;;;AAI3B,IAAA,IAAI,MAAM,CAAC,aAAa,EAAE;AACxB,QAAA,MAAM,CAAC,IAAI,CAAC,KAAK,mBAAmB,CAAA,CAAE,CAAC;;AAGzC,IAAA,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,CAAA,GAAA,EAAM,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAE,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;AACvF,IAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1C,OAAO,GAAG,CAAC,IAAI;AACjB;;AClDA;;AAEG;AACI,MAAM,eAAe,GAAoB;AAC9C,IAAA,IAAI,EAAE,OAAO;AACb,IAAA,OAAO,EAAE,UAAU;CACpB;AAED,MAAM,kBAAkB,GAAG,oCAAoC;AAC/D;;AAEG;AACH,SAAS,UAAU,CAAC,GAAW,EAAA;AAC7B,IAAA,OAAO,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC;AACrC;AAEA;;;;;;;;AAQG;MACU,kBAAkB,GAAiC,iBAAiB,CAC/E,cAAc,EACd,SAAS,GAAG,CAAC,6BAA6B,CAAC,GAAG,SAAS;AAGzD,SAAS,cAAc,CAAC,IAAY,EAAE,MAAyB,EAAA;AAC7D,IAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAA,EAAG,IAAI,CAAA,CAAA,EAAI,MAAM,CAAC,GAAG,CAAA,CAAE,CAAC;;IAE5C,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC;AACtC,IAAA,IAAI,MAAM,CAAC,KAAK,EAAE;AAChB,QAAA,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;;;AAIpD,IAAA,IAAI,MAAM,CAAC,aAAa,EAAE;QACxB,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC;;IAEhD,OAAO,GAAG,CAAC,IAAI;AACjB;;ACnCA;;AAEG;AACI,MAAM,iBAAiB,GAAoB;AAChD,IAAA,IAAI,EAAE,SAAS;AACf,IAAA,OAAO,EAAE,YAAY;CACtB;AAED,MAAM,oBAAoB,GAAG,sCAAsC;AAEnE;;;;AAIG;AACH,SAAS,YAAY,CAAC,GAAW,EAAA;AAC/B,IAAA,OAAO,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC;AACvC;AAEA;;;;;;;AAOG;AACG,SAAU,oBAAoB,CAAC,IAAa,EAAA;IAChD,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;QAC9B,MAAM,IAAIA,aAAY,CAAA,IAAA,kDAEpB,SAAS;AACP,YAAA,CAAA,6CAAA,EAAgD,IAAI,CAAO,KAAA,CAAA;AACzD,gBAAA,CAAA,uGAAA,CAAyG,CAC9G;;IAGH,IAAI,IAAI,EAAE;AACR,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC;AACzB,QAAA,IAAI,GAAG,GAAG,CAAC,MAAM;;AAGnB,IAAA,MAAM,QAAQ,GAAG,CAAC,MAAyB,KAAI;AAC7C,QAAA,OAAO,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC;AACvC,KAAC;AAED,IAAA,MAAM,SAAS,GAAe,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC;AAC3E,IAAA,OAAO,SAAS;AAClB;AAEA,MAAM,WAAW,GAAG,IAAI,GAAG,CAAiB;IAC1C,CAAC,QAAQ,EAAE,GAAG,CAAC;IACf,CAAC,KAAK,EAAE,KAAK,CAAC;IACd,CAAC,SAAS,EAAE,GAAG,CAAC;IAChB,CAAC,GAAG,EAAE,GAAG,CAAC;IACV,CAAC,UAAU,EAAE,UAAU,CAAC;AACzB,CAAA,CAAC;AAEF,SAAS,gBAAgB,CAAC,MAAyB,EAAE,IAAa,EAAA;;IAEhE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,YAAY,CAAC;AACzC,IAAA,GAAG,CAAC,QAAQ,GAAG,kBAAkB;AAEjC,IAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QAC7D,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG;;IAG/B,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC;AAEvC,IAAA,IAAI,MAAM,CAAC,KAAK,EAAE;AAChB,QAAA,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;;;;AAKpD,IAAA,MAAM,aAAa,GAAG,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC,IAAI,MAAM,CAAC,YAAY,GAAG,GAAG,CAAC;AACpF,IAAA,IAAI,MAAM,CAAC,aAAa,IAAI,CAAC,aAAa,EAAE;QAC1C,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC;;AAGhD,IAAA,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE;AACtE,QAAA,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC1B,YAAA,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAE,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC;;aAC1D;YACL,IAAI,SAAS,EAAE;gBACb,OAAO,CAAC,IAAI,CACVD,mBAAkB,CAAA,IAAA,kDAEhB,4FAA4F,KAAK,CAAA,IAAA,CAAM,CACxG,CACF;;;;;IAKP,OAAO,GAAG,CAAC,QAAQ,KAAK,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI;AAC3E;;AC5GA;SACgB,mBAAmB,CAAC,KAAa,EAAE,YAAY,GAAG,IAAI,EAAA;IACpE,MAAM,SAAS,GAAG;UACd,CAAoD,iDAAA,EAAA,KAAK,CAAO,KAAA;UAChE,EAAE;IACN,OAAO,CAAA,+BAAA,EAAkC,SAAS,CAAA,iBAAA,CAAmB;AACvE;;ACFA;;;;AAIG;AACG,SAAU,aAAa,CAAC,SAAiB,EAAA;IAC7C,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,IAAIC,aAAY,CAEpB,IAAA,gEAAA,CAAA,6BAAA,EAAgC,SAAS,CAAqB,mBAAA,CAAA;AAC5D,YAAA,CAAA,qEAAA,CAAuE,CAC1E;;AAEL;;ACIA;;;;;;;;;AASG;MAEU,gBAAgB,CAAA;;AAEnB,IAAA,MAAM,GAAG,IAAI,GAAG,EAA8B;AAE9C,IAAA,MAAM,GAAkB,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW;IACpD,QAAQ,GAA+B,IAAI;AAEnD,IAAA,WAAA,GAAA;QACE,aAAa,CAAC,aAAa,CAAC;QAE5B,IACE,CAAC,OAAO,YAAY,KAAK,WAAW,IAAI,CAAC,YAAY;AACrD,YAAA,OAAO,mBAAmB,KAAK,WAAW,EAC1C;AACA,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,EAAE;;;AAIlD;;;AAGG;IACK,uBAAuB,GAAA;QAC7B,MAAM,QAAQ,GAAG,IAAI,mBAAmB,CAAC,CAAC,SAAS,KAAI;AACrD,YAAA,MAAM,OAAO,GAAG,SAAS,CAAC,UAAU,EAAE;AACtC,YAAA,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;gBAAE;;;;;YAK1B,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;;;YAI9C,MAAM,MAAM,GAAI,UAAkB,CAAC,OAAO,EAAE,GAAG,IAAI,EAAE;;AAGrD,YAAA,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;gBAAE;YAE9D,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;AACnC,YAAA,IAAI,CAAC,GAAG;gBAAE;YACV,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE;AAC/C,gBAAA,GAAG,CAAC,qBAAqB,GAAG,IAAI;gBAChC,uBAAuB,CAAC,MAAM,CAAC;;YAEjC,IAAI,GAAG,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE;AAC9C,gBAAA,GAAG,CAAC,qBAAqB,GAAG,IAAI;gBAChC,kBAAkB,CAAC,MAAM,CAAC;;AAE9B,SAAC,CAAC;AACF,QAAA,QAAQ,CAAC,OAAO,CAAC,EAAC,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC;AACpE,QAAA,OAAO,QAAQ;;AAGjB,IAAA,aAAa,CAAC,YAAoB,EAAE,aAAqB,EAAE,UAAmB,EAAA;QAC5E,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE;AACpB,QAAA,MAAM,qBAAqB,GAAuB;AAChD,YAAA,QAAQ,EAAE,UAAU;AACpB,YAAA,QAAQ,EAAE,KAAK;AACf,YAAA,qBAAqB,EAAE,KAAK;AAC5B,YAAA,qBAAqB,EAAE,KAAK;SAC7B;AACD,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,MAAO,CAAC,CAAC,IAAI,EAAE,qBAAqB,CAAC;;AAGjF,IAAA,eAAe,CAAC,YAAoB,EAAA;QAClC,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE;AACpB,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,MAAO,CAAC,CAAC,IAAI,CAAC;;IAG7D,WAAW,CAAC,WAAmB,EAAE,MAAc,EAAA;QAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE;AACpB,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,MAAO,CAAC,CAAC,IAAI;QAC1D,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC;QACxC,IAAI,GAAG,EAAE;AACP,YAAA,GAAG,CAAC,QAAQ,GAAG,IAAI;AACnB,YAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAO,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC;AACvD,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC;;;IAInC,WAAW,GAAA;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;;kHApFV,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA;;sGAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AAyFhC,SAAS,uBAAuB,CAAC,KAAa,EAAA;AAC5C,IAAA,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,CAAC;AACnD,IAAA,OAAO,CAAC,KAAK,CACXD,mBAAkB,CAEhB,IAAA,kDAAA,CAAA,EAAG,gBAAgB,CAAoD,kDAAA,CAAA;QACrE,CAAqE,mEAAA,CAAA;QACrE,CAAiD,+CAAA,CAAA;QACjD,CAA4C,0CAAA,CAAA,CAC/C,CACF;AACH;AAEA,SAAS,kBAAkB,CAAC,KAAa,EAAA;AACvC,IAAA,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,CAAC;AACnD,IAAA,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAEhB,IAAA,gDAAA,CAAA,EAAG,gBAAgB,CAAoD,kDAAA,CAAA;QACrE,CAAqE,mEAAA,CAAA;QACrE,CAA0E,wEAAA,CAAA;QAC1E,CAAuD,qDAAA,CAAA,CAC1D,CACF;AACH;;ACjIA;AACA,MAAM,mCAAmC,GAAG,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;AAEnG;;;;;;;;;;;;;;;;;AAiBG;AACU,MAAA,0BAA0B,GAAG,IAAI,cAAc,CAC1D,SAAS,GAAG,4BAA4B,GAAG,EAAE;AAG/C;;;;;;AAMG;MAEU,qBAAqB,CAAA;AACxB,IAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AAEnC;;;AAGG;IACK,eAAe,GAAuB,IAAI;AAElD;;AAEG;AACK,IAAA,WAAW,GAAG,IAAI,GAAG,EAAU;AAE/B,IAAA,MAAM,GAAkB,IAAI,CAAC,QAAQ,CAAC,WAAW;AAEjD,IAAA,SAAS,GAAG,IAAI,GAAG,CAAS,mCAAmC,CAAC;AAExE,IAAA,WAAA,GAAA;QACE,aAAa,CAAC,yBAAyB,CAAC;AACxC,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,0BAA0B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QACtE,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;;;AAI7B,IAAA,iBAAiB,CAAC,OAA0C,EAAA;AAClE,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAC1B,YAAA,WAAW,CAAC,OAAO,EAAE,CAAC,MAAM,KAAI;gBAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAC7C,aAAC,CAAC;;aACG;YACL,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;;;AAIhD;;;;;;AAMG;IACH,gBAAgB,CAAC,YAAoB,EAAE,aAAqB,EAAA;AAC1D,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY;YAAE;QAEzD,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,MAAO,CAAC;QACjD,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;YAAE;;QAGhF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;;;;;AAMnC,QAAA,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,oBAAoB,EAAE;AAEpD,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC5C,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAAA,IAAA,6DAEhB,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAA+C,6CAAA,CAAA;gBAClF,CAAsF,oFAAA,CAAA;gBACtF,CAAkF,gFAAA,CAAA;gBAClF,CAA4C,0CAAA,CAAA;AAC5C,gBAAA,CAAA,+BAAA,EAAkC,MAAM,CAAC,MAAM,CAAI,EAAA,CAAA,CACtD,CACF;;;IAIG,oBAAoB,GAAA;AAC1B,QAAA,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU;QACxC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAkB,sBAAsB,CAAC;AACrF,QAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,YAAA,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAO,CAAC;AAC3C,YAAA,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;;AAEhC,QAAA,OAAO,cAAc;;IAGvB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE;AAC7B,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;kHApFf,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAArB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,cADT,MAAM,EAAA,CAAA;;sGAClB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBADjC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AAyFhC;;;AAGG;AACH,SAAS,WAAW,CAAI,KAAoB,EAAE,EAAsB,EAAA;AAClE,IAAA,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;QACvB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;;AAE7D;;AC9IA;;;;;AAKG;AACI,MAAM,8BAA8B,GAAG,CAAC;AAE/C;;;;;;AAMG;AACI,MAAM,gBAAgB,GAAG,IAAI,cAAc,CAChD,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,+BAA+B,GAAG,EAAE,EACpF;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,MAAM,IAAI,GAAG,EAAU;AACjC,CAAA,CACF;;ACXD;;;;;;;AAOG;MAEU,kBAAkB,CAAA;AACZ,IAAA,eAAe,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC1C,IAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;IACpC,UAAU,GAAG,KAAK;AAE1B;;;;;;;;;;;;;;;AAeG;AACH,IAAA,oBAAoB,CAAC,QAAmB,EAAE,GAAW,EAAE,MAAe,EAAE,KAAc,EAAA;AACpF,QAAA,IACE,SAAS;YACT,CAAC,IAAI,CAAC,UAAU;AAChB,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,8BAA8B,EAC3D;AACA,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI;AACtB,YAAA,OAAO,CAAC,IAAI,CACVA,mBAAkB,wDAEhB,CAAiE,+DAAA,CAAA;AAC/D,gBAAA,CAAA,EAAG,8BAA8B,CAAmC,iCAAA,CAAA;gBACpE,CAAmE,iEAAA,CAAA;gBACnE,CAA8E,4EAAA,CAAA,CACjF,CACF;;QAGH,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACjC;;AAGF,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC;QAE7B,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC;QAC9C,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC;QAC7C,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC;QAC3C,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC;QAChD,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC;QAEvD,IAAI,KAAK,EAAE;YACT,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,CAAC;;QAGrD,IAAI,MAAM,EAAE;YACV,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,aAAa,EAAE,MAAM,CAAC;;QAGvD,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;;kHA3DxC,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,cADN,MAAM,EAAA,CAAA;;sGAClB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAD9B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;ACuBhC;;;;;;AAMG;AACH,MAAM,8BAA8B,GAAG,EAAE;AAEzC;;;AAGG;AACH,MAAM,6BAA6B,GAAG,2BAA2B;AAEjE;;;AAGG;AACH,MAAM,+BAA+B,GAAG,mCAAmC;AAE3E;;;;AAIG;AACI,MAAM,2BAA2B,GAAG,CAAC;AAE5C;;;AAGG;AACI,MAAM,8BAA8B,GAAG,CAAC;AAE/C;;AAEG;AACH,MAAM,0BAA0B,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAEzC;;AAEG;AACH,MAAM,0BAA0B,GAAG,GAAG;AACtC;;AAEG;AACH,MAAM,sBAAsB,GAAG,GAAG;AAElC;;;;AAIG;AACH,MAAM,yBAAyB,GAAG,IAAI;AAEtC;;;AAGG;AACH,MAAM,wBAAwB,GAAG,IAAI;AACrC,MAAM,yBAAyB,GAAG,IAAI;AAEtC;;;AAGG;AACH,MAAM,2BAA2B,GAAG,IAAI;AAExC;;;;;;;;AAQG;AACI,MAAM,mBAAmB,GAAG,IAAI;AAChC,MAAM,oBAAoB,GAAG,KAAK;AAEzC;AACO,MAAM,gBAAgB,GAAG;IAC9B,eAAe;IACf,kBAAkB;IAClB,oBAAoB;IACpB,iBAAiB;CAClB;AAED;;AAEG;AACH,MAAM,wBAAwB,GAAG,EAAE;AAEnC;;;;AAIG;AACH,IAAI,6BAA6B,GAAG,CAAC;AAmBrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiGG;MAgBU,gBAAgB,CAAA;AACnB,IAAA,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC;IAClC,MAAM,GAAgB,aAAa,CAAC,MAAM,CAACE,aAAY,CAAC,CAAC;AACzD,IAAA,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;AAC5B,IAAA,UAAU,GAAqB,MAAM,CAAC,UAAU,CAAC,CAAC,aAAa;AAC/D,IAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC3B,IAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;;;AAI/B,IAAA,WAAW;AAEnB;;;;;AAKG;IACK,YAAY,GAAkB,IAAI;AAE1C;;;;AAIG;AACgD,IAAA,KAAK;AAExD;;;;;;;;;;AAUG;AACM,IAAA,QAAQ;AAEjB;;;AAGG;AACM,IAAA,KAAK;AAEd;;;AAGG;AACkC,IAAA,KAAK;AAE1C;;;AAGG;AACkC,IAAA,MAAM;AAE3C;;;;;;;;;AASG;AACM,IAAA,QAAQ;AAEjB;;;;;;;AAOG;AACM,IAAA,OAAO;AAEhB;;AAEG;IACmC,QAAQ,GAAG,KAAK;AAEtD;;AAEG;AACM,IAAA,YAAY;AAErB;;AAEG;IACmC,sBAAsB,GAAG,KAAK;AAEpE;;;AAGG;IACmC,IAAI,GAAG,KAAK;AAElD;;AAEG;AACwC,IAAA,WAAW;AAEtD;;;AAGG;AACM,IAAA,iBAAiB;AAE1B;;;;;AAKG;AACM,IAAA,GAAG;AAEZ;;;;;AAKG;AACM,IAAA,MAAM;AAEf,IAAA,WAAA,GAAA;QACE,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC;AAEtD,YAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAK;gBAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;oBAChD,IAAI,CAAC,WAAY,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;;AAExD,aAAC,CAAC;;;;IAKN,QAAQ,GAAA;QACNC,uBAAsB,CAAC,kBAAkB,CAAC;QAE1C,IAAI,SAAS,EAAE;YACb,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;YACxC,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC;AAC9C,YAAA,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC;YACxC,sBAAsB,CAAC,IAAI,CAAC;AAC5B,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,yBAAyB,CAAC,IAAI,CAAC;;YAEjC,oBAAoB,CAAC,IAAI,CAAC;YAC1B,gBAAgB,CAAC,IAAI,CAAC;AACtB,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,yBAAyB,CAAC,IAAI,CAAC;;;gBAG/B,MAAM,CAAC,iBAAiB,CAAC,MACvB,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CACnF;;iBACI;gBACL,4BAA4B,CAAC,IAAI,CAAC;AAClC,gBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;oBAC7B,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC;;AAEpD,gBAAA,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;oBAC5B,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC;;;;gBAIlD,MAAM,CAAC,iBAAiB,CAAC,MACvB,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAC/E;;YAEH,uBAAuB,CAAC,IAAI,CAAC;YAC7B,wBAAwB,CAAC,IAAI,CAAC;AAC9B,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,oBAAoB,CAAC,IAAI,CAAC;;AAE5B,YAAA,sBAAsB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC;YAC9C,6BAA6B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC;AAC3D,YAAA,6BAA6B,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC;AACrD,YAAA,iCAAiC,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC;AAEzD,YAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;AAC5B,gBAAA,IAAI,CAAC,WAAY,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC;AACpF,aAAC,CAAC;AAEF,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,qBAAqB,CAAC;AACxD,gBAAA,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC;gBAE5D,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,CAAC,YAAY,EAAE;oBACxD,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC;oBACxD,gCAAgC,CAAC,cAAc,CAAC;;;;AAItD,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC;;QAE/C,IAAI,CAAC,iBAAiB,EAAE;;IAGlB,iBAAiB,GAAA;;;AAGvB,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,IAAI,CAAC,KAAK,KAAK,OAAO;;aACjB;AACL,YAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAM,CAAC,QAAQ,EAAE,CAAC;AACtD,YAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAO,CAAC,QAAQ,EAAE,CAAC;;QAG1D,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3D,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC/D,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;;;AAIrD,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,CAAC;;;AAIvC,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE;AAEjD,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,MAAM,EAAE;gBACxC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;;iBAChD;gBACL,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC;;;aAEvC;YACL,IACE,IAAI,CAAC,QAAQ;AACb,gBAAA,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AACjD,gBAAA,IAAI,CAAC,kBAAkB,EAAE,KAAK,MAAM,EACpC;AACA,gBAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC;;;QAIjD,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE;YACxE,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC;AAChE,YAAA,kBAAkB,CAAC,oBAAoB,CACrC,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,eAAe,EAAE,EACtB,eAAe,EACf,IAAI,CAAC,KAAK,CACX;;;;AAKL,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,SAAS,EAAE;AACb,YAAA,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE;gBACzC,UAAU;gBACV,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,cAAc;gBACd,wBAAwB;AACzB,aAAA,CAAC;;AAEJ,QAAA,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;AACzD,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY;AAChC,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAE7B,IAAI,SAAS,EAAE;AACb,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY;gBAChC,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,KAAK,MAAM,EAAE;oBACzC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;AACxC,oBAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;wBAC5B,IAAI,CAAC,WAAY,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC;AAC/C,qBAAC,CAAC;;;;AAKR,QAAA,IACE,SAAS;AACT,YAAA,OAAO,CAAC,aAAa,CAAC,EAAE,YAAY;YACpC,OAAO,YAAY,KAAK,WAAW;YACnC,CAAC,YAAY,EACb;AACA,YAAA,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC;;;AAI9C,IAAA,eAAe,CACrB,yBAAkE,EAAA;QAElE,IAAI,eAAe,GAAsB,yBAAyB;AAClE,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,eAAe,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY;;AAElD,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;;IAGlC,kBAAkB,GAAA;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;YAChD,OAAO,IAAI,CAAC,OAAO;;QAErB,OAAO,IAAI,CAAC,QAAQ,GAAG,OAAO,GAAG,MAAM;;IAGjC,gBAAgB,GAAA;QACtB,OAAO,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,MAAM;;IAGhC,WAAW,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;;;;;AAKjB,YAAA,OAAO,MAAM;;;;;AAKf,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,MAAM;;IAGxB,eAAe,GAAA;;;;AAIrB,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,SAAS,GAAG,EAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAC;;YAEnC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;;QAErD,OAAO,IAAI,CAAC,YAAY;;IAGlB,kBAAkB,GAAA;QACxB,MAAM,WAAW,GAAG,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AACrE,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC;aACpB,KAAK,CAAC,GAAG;aACT,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,EAAE;AAC1B,aAAA,GAAG,CAAC,CAAC,MAAM,KAAI;AACd,YAAA,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE;YACtB,MAAM,KAAK,GAAG,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAM;AACjF,YAAA,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,EAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAC,CAAC,CAAI,CAAA,EAAA,MAAM,EAAE;AACtE,SAAC,CAAC;AACJ,QAAA,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;;IAGrB,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,OAAO,IAAI,CAAC,mBAAmB,EAAE;;aAC5B;AACL,YAAA,OAAO,IAAI,CAAC,cAAc,EAAE;;;IAIxB,mBAAmB,GAAA;AACzB,QAAA,MAAM,EAAC,WAAW,EAAC,GAAG,IAAI,CAAC,MAAM;QAEjC,IAAI,mBAAmB,GAAG,WAAY;QACtC,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,OAAO,EAAE;;;AAGlC,YAAA,mBAAmB,GAAG,WAAY,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,0BAA0B,CAAC;;AAGrF,QAAA,MAAM,SAAS,GAAG,mBAAmB,CAAC,GAAG,CACvC,CAAC,EAAE,KAAK,CAAG,EAAA,IAAI,CAAC,eAAe,CAAC,EAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,EAAC,CAAC,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,CAAG,CACvE;AACD,QAAA,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;;IAGrB,kBAAkB,CAAC,cAAc,GAAG,KAAK,EAAA;QAC/C,IAAI,cAAc,EAAE;;;AAGlB,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;AAG1B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE;AAC3C,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,YAAY,CAAC;QAE1C,IAAI,eAAe,GAAuB,SAAS;AACnD,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE;;AACtC,aAAA,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE;AAC/C,YAAA,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE;;QAG7C,IAAI,eAAe,EAAE;AACnB,YAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,eAAe,CAAC;;AAElD,QAAA,OAAO,eAAe;;IAGhB,cAAc,GAAA;AACpB,QAAA,MAAM,SAAS,GAAG,0BAA0B,CAAC,GAAG,CAC9C,CAAC,UAAU,KACT,CAAA,EAAG,IAAI,CAAC,eAAe,CAAC;YACtB,GAAG,EAAE,IAAI,CAAC,KAAK;AACf,YAAA,KAAK,EAAE,IAAI,CAAC,KAAM,GAAG,UAAU;AAChC,SAAA,CAAC,CAAI,CAAA,EAAA,UAAU,CAAG,CAAA,CAAA,CACtB;AACD,QAAA,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;;IAGrB,6BAA6B,GAAA;QACnC,IAAI,cAAc,GAAG,KAAK;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,cAAc;gBACZ,IAAI,CAAC,KAAM,GAAG,wBAAwB,IAAI,IAAI,CAAC,MAAO,GAAG,yBAAyB;;AAEtF,QAAA,QACE,CAAC,IAAI,CAAC,sBAAsB;YAC5B,CAAC,IAAI,CAAC,MAAM;YACZ,IAAI,CAAC,WAAW,KAAK,eAAe;YACpC,CAAC,cAAc;;AAInB;;;;AAIG;AACO,IAAA,mBAAmB,CAAC,gBAAkC,EAAA;AAC9D,QAAA,MAAM,EAAC,qBAAqB,EAAC,GAAG,IAAI,CAAC,MAAM;AAC3C,QAAA,IAAI,gBAAgB,KAAK,IAAI,EAAE;AAC7B,YAAA,OAAO,CAAO,IAAA,EAAA,IAAI,CAAC,eAAe,CAAC;gBACjC,GAAG,EAAE,IAAI,CAAC,KAAK;AACf,gBAAA,KAAK,EAAE,qBAAqB;AAC5B,gBAAA,aAAa,EAAE,IAAI;AACpB,aAAA,CAAC,GAAG;;AACA,aAAA,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;YAC/C,OAAO,CAAA,IAAA,EAAO,gBAAgB,CAAA,CAAA,CAAG;;AAEnC,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACO,IAAA,qBAAqB,CAAC,iBAA0C,EAAA;QACxE,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;AACnE,YAAA,OAAO,IAAI;;AAEb,QAAA,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC;;AAGhC,IAAA,uBAAuB,CAAC,GAAqB,EAAA;QACnD,MAAM,QAAQ,GAAG,MAAK;YACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,iBAAiB,CAAC;AAC9D,YAAA,oBAAoB,EAAE;AACtB,YAAA,qBAAqB,EAAE;AACvB,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;YACxB,iBAAiB,CAAC,YAAY,EAAE;AAClC,SAAC;AAED,QAAA,MAAM,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC;AACxE,QAAA,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC;;;;AAK1E,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAK;AAC7B,YAAA,oBAAoB,EAAE;AACtB,YAAA,qBAAqB,EAAE;AACzB,SAAC,CAAC;AAEF,QAAA,yBAAyB,CAAC,GAAG,EAAE,QAAQ,CAAC;;IAGlC,gBAAgB,CAAC,IAAY,EAAE,KAAa,EAAA;AAClD,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC;;kHAhe/C,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,gBAAgB,EAyoCpB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,aAAa,CAvlCD,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,eAAe,CAMf,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,eAAe,CA2Bf,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAUhB,EAAA,YAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,CAAA,wBAAA,EAAA,wBAAA,EAAA,gBAAgB,CAMhB,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,gBAAgB,+CA+iCrB,qBAAqB,CAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,GAAA,EAAA,KAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gBAAA,EAAA,4BAAA,EAAA,aAAA,EAAA,wBAAA,EAAA,cAAA,EAAA,wBAAA,EAAA,aAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,gCAAA,EAAA,2BAAA,EAAA,kCAAA,EAAA,yBAAA,EAAA,oCAAA,EAAA,wBAAA,EAAA,uDAAA,EAAA,cAAA,EAAA,iFAAA,EAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGAlpCxB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAf5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,IAAI,EAAE;AACJ,wBAAA,kBAAkB,EAAE,0BAA0B;AAC9C,wBAAA,eAAe,EAAE,sBAAsB;AACvC,wBAAA,gBAAgB,EAAE,sBAAsB;AACxC,wBAAA,eAAe,EAAE,mBAAmB;AACpC,wBAAA,yBAAyB,EAAE,8BAA8B;AACzD,wBAAA,6BAA6B,EAAE,gCAAgC;AAC/D,wBAAA,2BAA2B,EAAE,kCAAkC;AAC/D,wBAAA,0BAA0B,EAAE,uDAAuD;AACnF,wBAAA,gBAAgB,EACd,+EAA+E;AAClF,qBAAA;AACF,iBAAA;;sBA0BE,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAC;;sBAahD;;sBAMA;;sBAMA,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;;sBAMlC,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;;sBAYlC;;sBAUA;;sBAKA,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;sBAKnC;;sBAKA,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;sBAMnC,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;sBAKnC,KAAK;uBAAC,EAAC,SAAS,EAAE,qBAAqB,EAAC;;sBAMxC;;sBAQA;;sBAQA;;AAsWH;AAEA;;AAEG;AACH,SAAS,aAAa,CAAC,MAAmB,EAAA;IACxC,IAAI,iBAAiB,GAA6B,EAAE;AACpD,IAAA,IAAI,MAAM,CAAC,WAAW,EAAE;QACtB,iBAAiB,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;AAE1E,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAEC,sBAAqB,EAAE,MAAM,EAAE,iBAAiB,CAAC;AAC5E;AAEA;AAEA;;AAEG;AACH,SAAS,sBAAsB,CAAC,GAAqB,EAAA;AACnD,IAAA,IAAI,GAAG,CAAC,GAAG,EAAE;QACX,MAAM,IAAIH,aAAY,CAAA,IAAA,6CAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA6C,2CAAA,CAAA;YAC5E,CAA0D,wDAAA,CAAA;YAC1D,CAAsF,oFAAA,CAAA;AACtF,YAAA,CAAA,iDAAA,CAAmD,CACtD;;AAEL;AAEA;;AAEG;AACH,SAAS,yBAAyB,CAAC,GAAqB,EAAA;AACtD,IAAA,IAAI,GAAG,CAAC,MAAM,EAAE;QACd,MAAM,IAAIA,aAAY,CAAA,IAAA,gDAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAmD,iDAAA,CAAA;YAClF,CAA0D,wDAAA,CAAA;YAC1D,CAA8E,4EAAA,CAAA;AAC9E,YAAA,CAAA,kEAAA,CAAoE,CACvE;;AAEL;AAEA;;AAEG;AACH,SAAS,oBAAoB,CAAC,GAAqB,EAAA;IACjD,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE;AAC5B,IAAA,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AAC7B,QAAA,IAAI,KAAK,CAAC,MAAM,GAAG,8BAA8B,EAAE;YACjD,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,8BAA8B,CAAC,GAAG,KAAK;;AAEpE,QAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAwC,sCAAA,CAAA;AAC9E,YAAA,CAAA,CAAA,EAAI,KAAK,CAA+D,6DAAA,CAAA;YACxE,CAAuE,qEAAA,CAAA;AACvE,YAAA,CAAA,qEAAA,CAAuE,CAC1E;;AAEL;AAEA;;AAEG;AACH,SAAS,oBAAoB,CAAC,GAAqB,EAAA;AACjD,IAAA,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK;AACrB,IAAA,IAAI,KAAK,EAAE,KAAK,CAAC,mBAAmB,CAAC,EAAE;AACrC,QAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAA2C,yCAAA,CAAA;YACjF,CAA4F,0FAAA,CAAA;YAC5F,CAAkF,gFAAA,CAAA;AAClF,YAAA,CAAA,6FAAA,CAA+F,CAClG;;AAEL;AAEA,SAAS,sBAAsB,CAAC,GAAqB,EAAE,WAAwB,EAAA;IAC7E,2CAA2C,CAAC,GAAG,CAAC;AAChD,IAAA,wCAAwC,CAAC,GAAG,EAAE,WAAW,CAAC;IAC1D,wBAAwB,CAAC,GAAG,CAAC;AAC/B;AAEA;;AAEG;AACH,SAAS,2CAA2C,CAAC,GAAqB,EAAA;IACxE,IAAI,GAAG,CAAC,iBAAiB,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE;AAC7C,QAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CACpB,GAAG,CAAC,KAAK,EACT,KAAK,CACN,CAAsD,oDAAA,CAAA;AACrD,YAAA,CAAA,+EAAA,CAAiF,CACpF;;AAEL;AAEA;;;AAGG;AACH,SAAS,wCAAwC,CAAC,GAAqB,EAAE,WAAwB,EAAA;IAC/F,IAAI,GAAG,CAAC,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,eAAe,EAAE;QAC/D,MAAM,IAAIA,aAAY,CAAA,IAAA,kDAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAoD,kDAAA,CAAA;YACnF,CAAsE,oEAAA,CAAA;YACtE,CAA6F,2FAAA,CAAA;AAC7F,YAAA,CAAA,qFAAA,CAAuF,CAC1F;;AAEL;AAEA;;AAEG;AACH,SAAS,wBAAwB,CAAC,GAAqB,EAAA;IACrD,IACE,GAAG,CAAC,WAAW;AACf,QAAA,OAAO,GAAG,CAAC,WAAW,KAAK,QAAQ;QACnC,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,EACnC;QACA,IAAI,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,oBAAoB,EAAE;YACjD,MAAM,IAAIA,aAAY,CAAA,IAAA,+CAEpB,CAAG,EAAA,mBAAmB,CACpB,GAAG,CAAC,KAAK,CACV,CAAsE,oEAAA,CAAA;AACrE,gBAAA,CAAA,KAAA,EAAQ,oBAAoB,CAA0E,wEAAA,CAAA;gBACtG,CAAqG,mGAAA,CAAA;AACrG,gBAAA,CAAA,+BAAA,CAAiC,CACpC;;QAEH,IAAI,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,mBAAmB,EAAE;AAChD,YAAA,OAAO,CAAC,IAAI,CACVD,mBAAkB,CAEhB,IAAA,+CAAA,CAAA,EAAG,mBAAmB,CACpB,GAAG,CAAC,KAAK,CACV,CAAsE,oEAAA,CAAA;AACrE,gBAAA,CAAA,KAAA,EAAQ,mBAAmB,CAAiE,+DAAA,CAAA;gBAC5F,CAA+G,6GAAA,CAAA;gBAC/G,CAA0C,wCAAA,CAAA,CAC7C,CACF;;;AAGP;AAEA;;AAEG;AACH,SAAS,gBAAgB,CAAC,GAAqB,EAAA;IAC7C,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE;AAC9B,IAAA,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AAC7B,QAAA,MAAM,IAAIC,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAqC,kCAAA,EAAA,KAAK,CAAK,GAAA,CAAA;YAC9E,CAAiE,+DAAA,CAAA;YACjE,CAAuE,qEAAA,CAAA;AACvE,YAAA,CAAA,oEAAA,CAAsE,CACzE;;AAEL;AAEA;;AAEG;AACH,SAAS,mBAAmB,CAAC,GAAqB,EAAE,IAAY,EAAE,KAAc,EAAA;AAC9E,IAAA,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ;IAC1C,MAAM,aAAa,GAAG,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE;AACrD,IAAA,IAAI,CAAC,QAAQ,IAAI,aAAa,EAAE;AAC9B,QAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAM,GAAA,EAAA,IAAI,CAA0B,wBAAA,CAAA;YACnE,CAAM,GAAA,EAAA,KAAK,CAA2D,yDAAA,CAAA,CACzE;;AAEL;AAEA;;AAEG;AACa,SAAA,mBAAmB,CAAC,GAAqB,EAAE,KAAc,EAAA;IACvE,IAAI,KAAK,IAAI,IAAI;QAAE;AACnB,IAAA,mBAAmB,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,CAAC;IAC3C,MAAM,SAAS,GAAG,KAAe;IACjC,MAAM,sBAAsB,GAAG,6BAA6B,CAAC,IAAI,CAAC,SAAS,CAAC;IAC5E,MAAM,wBAAwB,GAAG,+BAA+B,CAAC,IAAI,CAAC,SAAS,CAAC;IAEhF,IAAI,wBAAwB,EAAE;AAC5B,QAAA,qBAAqB,CAAC,GAAG,EAAE,SAAS,CAAC;;AAGvC,IAAA,MAAM,aAAa,GAAG,sBAAsB,IAAI,wBAAwB;IACxE,IAAI,CAAC,aAAa,EAAE;AAClB,QAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAyC,sCAAA,EAAA,KAAK,CAAO,KAAA,CAAA;YACpF,CAAqF,mFAAA,CAAA;AACrF,YAAA,CAAA,uEAAA,CAAyE,CAC5E;;AAEL;AAEA,SAAS,qBAAqB,CAAC,GAAqB,EAAE,KAAa,EAAA;IACjE,MAAM,eAAe,GAAG;SACrB,KAAK,CAAC,GAAG;AACT,SAAA,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,EAAE,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,2BAA2B,CAAC;IAC/E,IAAI,CAAC,eAAe,EAAE;QACpB,MAAM,IAAIA,aAAY,CAAA,IAAA,uCAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA0D,wDAAA,CAAA;AACzF,YAAA,CAAA,EAAA,EAAK,KAAK,CAAmE,iEAAA,CAAA;AAC7E,YAAA,CAAA,EAAG,8BAA8B,CAAuC,qCAAA,CAAA;AACxE,YAAA,CAAA,EAAG,2BAA2B,CAA8D,4DAAA,CAAA;AAC5F,YAAA,CAAA,aAAA,EAAgB,8BAA8B,CAAuC,qCAAA,CAAA;YACrF,CAA0F,wFAAA,CAAA;YAC1F,CAAG,EAAA,2BAA2B,CAAoE,kEAAA,CAAA,CACrG;;AAEL;AAEA;;;AAGG;AACH,SAAS,wBAAwB,CAAC,GAAqB,EAAE,SAAiB,EAAA;AACxE,IAAA,IAAI,MAAe;IACnB,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,QAAQ,EAAE;QACnD,MAAM;AACJ,YAAA,CAAA,WAAA,EAAc,SAAS,CAA6C,2CAAA,CAAA;AACpE,gBAAA,CAAA,0EAAA,CAA4E;;SACzE;QACL,MAAM;AACJ,YAAA,CAAA,eAAA,EAAkB,SAAS,CAA4C,0CAAA,CAAA;AACvE,gBAAA,CAAA,iEAAA,CAAmE;;AAEvE,IAAA,OAAO,IAAIA,aAAY,CAErB,IAAA,iDAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAM,GAAA,EAAA,SAAS,CAAuC,qCAAA,CAAA;AACrF,QAAA,CAAA,oEAAA,EAAuE,MAAM,CAAG,CAAA,CAAA;AAChF,QAAA,CAAA,6BAAA,EAAgC,SAAS,CAAuB,qBAAA,CAAA;AAChE,QAAA,CAAA,yEAAA,CAA2E,CAC9E;AACH;AAEA;;AAEG;AACH,SAAS,2BAA2B,CAClC,GAAqB,EACrB,OAAsB,EACtB,MAAgB,EAAA;AAEhB,IAAA,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;QACvB,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC;QAC/C,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,EAAE;AAChD,YAAA,IAAI,KAAK,KAAK,OAAO,EAAE;;;;;gBAKrB,GAAG,GAAG,EAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,aAAa,EAAqB;;AAEjE,YAAA,MAAM,wBAAwB,CAAC,GAAG,EAAE,KAAK,CAAC;;AAE9C,KAAC,CAAC;AACJ;AAEA;;AAEG;AACH,SAAS,qBAAqB,CAAC,GAAqB,EAAE,UAAmB,EAAE,SAAiB,EAAA;IAC1F,MAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,GAAG,CAAC;IACpE,MAAM,WAAW,GACf,OAAO,UAAU,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC;AAC/F,IAAA,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,EAAE;AAChC,QAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAM,GAAA,EAAA,SAAS,CAA2B,yBAAA,CAAA;YACzE,CAA0B,uBAAA,EAAA,SAAS,CAAgC,8BAAA,CAAA,CACtE;;AAEL;AAEA;;;;AAIG;AACH,SAAS,uBAAuB,CAC9B,GAAqB,EACrB,GAAqB,EACrB,QAAmB,EACnB,UAAsB,EAAA;IAEtB,MAAM,QAAQ,GAAG,MAAK;AACpB,QAAA,oBAAoB,EAAE;AACtB,QAAA,qBAAqB,EAAE;QACvB,MAAM,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC;QAClD,IAAI,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACvE,IAAI,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACzE,MAAM,SAAS,GAAG,aAAa,CAAC,gBAAgB,CAAC,YAAY,CAAC;AAE9D,QAAA,IAAI,SAAS,KAAK,YAAY,EAAE;YAC9B,MAAM,UAAU,GAAG,aAAa,CAAC,gBAAgB,CAAC,aAAa,CAAC;YAChE,MAAM,YAAY,GAAG,aAAa,CAAC,gBAAgB,CAAC,eAAe,CAAC;YACpE,MAAM,aAAa,GAAG,aAAa,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;YACtE,MAAM,WAAW,GAAG,aAAa,CAAC,gBAAgB,CAAC,cAAc,CAAC;YAClE,aAAa,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC;YACnE,cAAc,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,aAAa,CAAC;;AAGtE,QAAA,MAAM,mBAAmB,GAAG,aAAa,GAAG,cAAc;QAC1D,MAAM,yBAAyB,GAAG,aAAa,KAAK,CAAC,IAAI,cAAc,KAAK,CAAC;AAE7E,QAAA,MAAM,cAAc,GAAG,GAAG,CAAC,YAAY;AACvC,QAAA,MAAM,eAAe,GAAG,GAAG,CAAC,aAAa;AACzC,QAAA,MAAM,oBAAoB,GAAG,cAAc,GAAG,eAAe;AAE7D,QAAA,MAAM,aAAa,GAAG,GAAG,CAAC,KAAM;AAChC,QAAA,MAAM,cAAc,GAAG,GAAG,CAAC,MAAO;AAClC,QAAA,MAAM,mBAAmB,GAAG,aAAa,GAAG,cAAc;;;;;;AAO1D,QAAA,MAAM,oBAAoB,GACxB,IAAI,CAAC,GAAG,CAAC,mBAAmB,GAAG,oBAAoB,CAAC,GAAG,sBAAsB;QAC/E,MAAM,iBAAiB,GACrB,yBAAyB;YACzB,IAAI,CAAC,GAAG,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,GAAG,sBAAsB;QAE/E,IAAI,oBAAoB,EAAE;AACxB,YAAA,OAAO,CAAC,IAAI,CACVD,mBAAkB,CAEhB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAgD,8CAAA,CAAA;gBAC/E,CAAiE,+DAAA,CAAA;gBACjE,CAA2B,wBAAA,EAAA,cAAc,CAAO,IAAA,EAAA,eAAe,CAAI,EAAA,CAAA;AACnE,gBAAA,CAAA,eAAA,EAAkB,KAAK,CACrB,oBAAoB,CACrB,CAA6C,2CAAA,CAAA;gBAC9C,CAAG,EAAA,aAAa,OAAO,cAAc,CAAA,iBAAA,EAAoB,KAAK,CAC5D,mBAAmB,CACpB,CAAK,GAAA,CAAA;gBACN,CAAwD,sDAAA,CAAA,CAC3D,CACF;;aACI,IAAI,iBAAiB,EAAE;AAC5B,YAAA,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAEhB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA0C,wCAAA,CAAA;gBACzE,CAAqD,mDAAA,CAAA;gBACrD,CAA2B,wBAAA,EAAA,cAAc,CAAO,IAAA,EAAA,eAAe,CAAI,EAAA,CAAA;AACnE,gBAAA,CAAA,eAAA,EAAkB,KAAK,CAAC,oBAAoB,CAAC,CAA4B,0BAAA,CAAA;gBACzE,CAAG,EAAA,aAAa,CAAO,IAAA,EAAA,cAAc,CAAmB,iBAAA,CAAA;AACxD,gBAAA,CAAA,EAAG,KAAK,CAAC,mBAAmB,CAAC,CAAoD,kDAAA,CAAA;gBACjF,CAAsE,oEAAA,CAAA;gBACtE,CAAmE,iEAAA,CAAA;gBACnE,CAAuE,qEAAA,CAAA;gBACvE,CAAa,WAAA,CAAA,CAChB,CACF;;AACI,aAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,yBAAyB,EAAE;;AAErD,YAAA,MAAM,gBAAgB,GAAG,8BAA8B,GAAG,aAAa;AACvE,YAAA,MAAM,iBAAiB,GAAG,8BAA8B,GAAG,cAAc;AACzE,YAAA,MAAM,cAAc,GAAG,cAAc,GAAG,gBAAgB,IAAI,yBAAyB;AACrF,YAAA,MAAM,eAAe,GAAG,eAAe,GAAG,iBAAiB,IAAI,yBAAyB;AACxF,YAAA,IAAI,cAAc,IAAI,eAAe,EAAE;AACrC,gBAAA,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAEhB,IAAA,yCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAwC,sCAAA,CAAA;oBACvE,CAAyB,uBAAA,CAAA;oBACzB,CAA0B,uBAAA,EAAA,aAAa,CAAO,IAAA,EAAA,cAAc,CAAK,GAAA,CAAA;oBACjE,CAA2B,wBAAA,EAAA,cAAc,CAAO,IAAA,EAAA,eAAe,CAAK,GAAA,CAAA;oBACpE,CAAuC,oCAAA,EAAA,gBAAgB,CAAO,IAAA,EAAA,iBAAiB,CAAK,GAAA,CAAA;oBACpF,CAAmF,iFAAA,CAAA;AACnF,oBAAA,CAAA,EAAG,8BAA8B,CAA8C,4CAAA,CAAA;oBAC/E,CAA0D,wDAAA,CAAA,CAC7D,CACF;;;AAGP,KAAC;AAED,IAAA,MAAM,oBAAoB,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC;;;;;IAMnE,MAAM,qBAAqB,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,MAAK;AAC/D,QAAA,oBAAoB,EAAE;AACtB,QAAA,qBAAqB,EAAE;AACzB,KAAC,CAAC;;;;AAKF,IAAA,UAAU,CAAC,SAAS,CAAC,MAAK;AACxB,QAAA,oBAAoB,EAAE;AACtB,QAAA,qBAAqB,EAAE;AACzB,KAAC,CAAC;AAEF,IAAA,yBAAyB,CAAC,GAAG,EAAE,QAAQ,CAAC;AAC1C;AAEA;;AAEG;AACH,SAAS,4BAA4B,CAAC,GAAqB,EAAA;IACzD,IAAI,iBAAiB,GAAG,EAAE;AAC1B,IAAA,IAAI,GAAG,CAAC,KAAK,KAAK,SAAS;AAAE,QAAA,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5D,IAAA,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS;AAAE,QAAA,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC9D,IAAA,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAChC,MAAM,IAAIC,aAAY,CAAA,IAAA,gDAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA6B,2BAAA,CAAA;AAC5D,YAAA,CAAA,aAAA,EAAgB,iBAAiB,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAI,CAAA,EAAA,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAI,EAAA,CAAA;YAC3E,CAAsF,oFAAA,CAAA;YACtF,CAAmF,iFAAA,CAAA;AACnF,YAAA,CAAA,wCAAA,CAA0C,CAC7C;;AAEL;AAEA;;;AAGG;AACH,SAAS,yBAAyB,CAAC,GAAqB,EAAA;IACtD,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;QAC3B,MAAM,IAAIA,aAAY,CAAA,IAAA,uCAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA0D,wDAAA,CAAA;YACzF,CAAkG,gGAAA,CAAA;AAClG,YAAA,CAAA,kEAAA,CAAoE,CACvE;;AAEL;AAEA;;;AAGG;AACH,SAAS,2BAA2B,CAClC,GAAqB,EACrB,GAAqB,EACrB,QAAmB,EACnB,UAAsB,EAAA;IAEtB,MAAM,QAAQ,GAAG,MAAK;AACpB,QAAA,oBAAoB,EAAE;AACtB,QAAA,qBAAqB,EAAE;AACvB,QAAA,MAAM,cAAc,GAAG,GAAG,CAAC,YAAY;QACvC,IAAI,GAAG,CAAC,IAAI,IAAI,cAAc,KAAK,CAAC,EAAE;AACpC,YAAA,OAAO,CAAC,IAAI,CACVD,mBAAkB,CAEhB,IAAA,uCAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA8C,4CAAA,CAAA;gBAC7E,CAAiF,+EAAA,CAAA;gBACjF,CAA4E,0EAAA,CAAA;gBAC5E,CAA8E,4EAAA,CAAA;gBAC9E,CAA6D,2DAAA,CAAA,CAChE,CACF;;AAEL,KAAC;AAED,IAAA,MAAM,oBAAoB,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC;;IAGnE,MAAM,qBAAqB,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,MAAK;AAC/D,QAAA,oBAAoB,EAAE;AACtB,QAAA,qBAAqB,EAAE;AACzB,KAAC,CAAC;;;;AAKF,IAAA,UAAU,CAAC,SAAS,CAAC,MAAK;AACxB,QAAA,oBAAoB,EAAE;AACtB,QAAA,qBAAqB,EAAE;AACzB,KAAC,CAAC;AAEF,IAAA,yBAAyB,CAAC,GAAG,EAAE,QAAQ,CAAC;AAC1C;AAEA;;;AAGG;AACH,SAAS,uBAAuB,CAAC,GAAqB,EAAA;IACpD,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,QAAQ,EAAE;QAC/B,MAAM,IAAIC,aAAY,CAAA,IAAA,uCAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA6B,2BAAA,CAAA;YAC5D,CAAmD,iDAAA,CAAA;YACnD,CAAwD,sDAAA,CAAA;YACxD,CAAsD,oDAAA,CAAA;AACtD,YAAA,CAAA,oEAAA,CAAsE,CACzE;;IAEH,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;AAC7C,IAAA,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;QACzE,MAAM,IAAIA,aAAY,CAAA,IAAA,uCAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA6B,2BAAA,CAAA;YAC5D,CAA2B,wBAAA,EAAA,GAAG,CAAC,OAAO,CAAO,KAAA,CAAA;AAC7C,YAAA,CAAA,gEAAA,CAAkE,CACrE;;AAEL;AAEA;;AAEG;AACH,SAAS,wBAAwB,CAAC,GAAqB,EAAA;IACrD,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;AAC7C,IAAA,IAAI,OAAO,GAAG,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;QAC3E,MAAM,IAAIA,aAAY,CAAA,IAAA,uCAEpB,CAAG,EAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA8B,4BAAA,CAAA;YAC7D,CAA2B,wBAAA,EAAA,GAAG,CAAC,QAAQ,CAAO,KAAA,CAAA;AAC9C,YAAA,CAAA,gEAAA,CAAkE,CACrE;;AAEL;AAEA;;;;;;;;AAQG;AACH,SAAS,6BAA6B,CAAC,KAAa,EAAE,WAAwB,EAAA;AAC5E,IAAA,IAAI,WAAW,KAAK,eAAe,EAAE;QACnC,IAAI,iBAAiB,GAAG,EAAE;AAC1B,QAAA,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE;AACrC,YAAA,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACzB,gBAAA,iBAAiB,GAAG,MAAM,CAAC,IAAI;gBAC/B;;;QAGJ,IAAI,iBAAiB,EAAE;AACrB,YAAA,OAAO,CAAC,IAAI,CACVD,mBAAkB,qDAEhB,CAAmE,iEAAA,CAAA;AACjE,gBAAA,CAAA,EAAG,iBAAiB,CAA4C,0CAAA,CAAA;gBAChE,CAA8D,4DAAA,CAAA;AAC9D,gBAAA,CAAA,iCAAA,EAAoC,iBAAiB,CAAa,WAAA,CAAA;gBAClE,CAAiE,+DAAA,CAAA;gBACjE,CAAgE,8DAAA,CAAA;gBAChE,CAA6D,2DAAA,CAAA,CAChE,CACF;;;AAGP;AAEA;;AAEG;AACH,SAAS,6BAA6B,CAAC,GAAqB,EAAE,WAAwB,EAAA;IACpF,IAAI,GAAG,CAAC,QAAQ,IAAI,WAAW,KAAK,eAAe,EAAE;AACnD,QAAA,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAEhB,IAAA,kDAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAA6C,2CAAA,CAAA;YAC5E,CAAsE,oEAAA,CAAA;YACtE,CAA4E,0EAAA,CAAA;YAC5E,CAAoF,kFAAA,CAAA,CACvF,CACF;;AAEL;AAEA;;;AAGG;AACH,SAAS,iCAAiC,CAAC,GAAqB,EAAE,WAAwB,EAAA;IACxF,IAAI,GAAG,CAAC,YAAY,IAAI,WAAW,KAAK,eAAe,EAAE;AACvD,QAAA,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAEhB,IAAA,kDAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAiD,+CAAA,CAAA;YAChF,CAAsE,oEAAA,CAAA;YACtE,CAA2F,yFAAA,CAAA;YAC3F,CAA+F,6FAAA,CAAA,CAClG,CACF;;AAEL;AAEA;;AAEG;AACH,eAAe,gCAAgC,CAAC,MAAsB,EAAA;AACpE,IAAA,IAAI,6BAA6B,KAAK,CAAC,EAAE;AACvC,QAAA,6BAA6B,EAAE;AAC/B,QAAA,MAAM,MAAM,CAAC,UAAU,EAAE;AACzB,QAAA,IAAI,6BAA6B,GAAG,wBAAwB,EAAE;YAC5D,OAAO,CAAC,IAAI,CACVA,mBAAkB,2DAEhB,CAAuE,oEAAA,EAAA,wBAAwB,CAAW,QAAA,EAAA,6BAA6B,CAAW,SAAA,CAAA;gBAChJ,CAAoG,kGAAA,CAAA;gBACpG,CAAmF,iFAAA,CAAA,CACtF,CACF;;;SAEE;AACL,QAAA,6BAA6B,EAAE;;AAEnC;AAEA;;;;AAIG;AACH,SAAS,2BAA2B,CAAC,GAAqB,EAAE,UAA4B,EAAA;IACtF,MAAM,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;IACzD,IAAI,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACvE,IAAI,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAEzE,IAAI,aAAa,GAAG,2BAA2B,IAAI,cAAc,GAAG,2BAA2B,EAAE;AAC/F,QAAA,OAAO,CAAC,IAAI,CACVA,mBAAkB,CAEhB,IAAA,8DAAA,CAAA,EAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAiD,+CAAA,CAAA;AAChF,YAAA,CAAA,mEAAA,EAAsE,2BAA2B,CAAM,IAAA,CAAA;YACvG,CAAoD,kDAAA,CAAA,CACvD,CACF;;AAEL;AAEA,SAAS,yBAAyB,CAAC,GAAqB,EAAE,QAAsB,EAAA;;;;;;;;;;;IAW9E,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,YAAY,EAAE;AACpC,QAAA,QAAQ,EAAE;;AAEd;AAEA,SAAS,KAAK,CAAC,KAAa,EAAA;AAC1B,IAAA,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAC3D;AAEA;AACA;AACA,SAAS,aAAa,CAAC,KAAyB,EAAA;AAC9C,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,OAAO,KAAK;;AAEd,IAAA,OAAOK,gBAAe,CAAC,KAAK,CAAC;AAC/B;AAEA;AACA;AACM,SAAU,qBAAqB,CAAC,KAAuB,EAAA;AAC3D,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE,EAAE;AACtF,QAAA,OAAO,KAAK;;AAEd,IAAA,OAAO,gBAAgB,CAAC,KAAK,CAAC;AAChC;;;;"}