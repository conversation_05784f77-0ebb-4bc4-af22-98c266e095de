import { w as workerInit } from '../chunks/init-threads.BfqfWDNi.js';
import { r as runVmTests } from '../chunks/vm.tWlKAMXr.js';
import 'node:worker_threads';
import '../chunks/init.D-GGeAxo.js';
import 'node:fs';
import 'node:module';
import 'node:url';
import 'pathe';
import 'vite/module-runner';
import '../chunks/startModuleRunner.BEYtrq5Y.js';
import '@vitest/utils/helpers';
import '../path.js';
import 'node:path';
import '@vitest/utils/serialize';
import '../module-evaluator.js';
import 'node:vm';
import '../chunks/traces.U4xDYhzZ.js';
import '@vitest/mocker';
import '../chunks/index.QWbK7rHY.js';
import 'node:console';
import '@vitest/utils/error';
import '../chunks/rpc.BytlcPfC.js';
import '@vitest/utils/timers';
import '../chunks/index.0kCJoeWi.js';
import '../chunks/utils.DvEY5TfP.js';
import '@vitest/utils/source-map';
import '../chunks/inspector.CvyFGlXm.js';
import '../chunks/evaluatedModules.Dg1zASAC.js';
import '../chunks/console.Cf-YriPC.js';
import 'node:stream';
import 'tinyrainbow';
import '../chunks/date.Bq6ZW5rf.js';
import '@vitest/utils/resolver';
import '@vitest/utils/constants';

workerInit({ runTests: runVmTests });
