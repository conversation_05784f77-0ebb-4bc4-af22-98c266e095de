export { aR as BaseR<PERSON>orter, aS as BenchmarkBuiltinReporters, aE as BenchmarkReporter, aF as BenchmarkReportsMap, aT as BuiltinReporterOptions, aU as BuiltinReporters, aG as DefaultReporter, aH as DotReporter, aI as GithubActionsReporter, aJ as HangingProcessReporter, aL as JUnitReporter, aV as JsonAssertionResult, aK as JsonReporter, aW as JsonTestResult, aX as JsonTestResults, aB as ReportedHookContext, aC as Reporter, aM as ReportersMap, aN as TapFlatReporter, aO as TapReporter, aD as TestRunEndReason, aP as VerboseBenchmarkReporter, aQ as VerboseReporter } from './chunks/reporters.d.C2PtoEFY.js';
import '@vitest/runner';
import '@vitest/utils';
import './chunks/worker.d.Dxl5oW0C.js';
import 'vite/module-runner';
import './chunks/traces.d.Brik_NWu.js';
import '@vitest/pretty-format';
import '@vitest/snapshot';
import '@vitest/utils/diff';
import './chunks/environment.d.CrsxCzP1.js';
import 'node:stream';
import 'vite';
import '@vitest/mocker';
import '@vitest/utils/source-map';
import 'vitest/browser';
import './chunks/browser.d.Bq3zc1l_.js';
import '@vitest/expect';
import 'vitest/optional-types.js';
import './chunks/benchmark.d.DAaHLpsq.js';
import '@vitest/runner/utils';
import 'tinybench';
import './chunks/coverage.d.BZtK59WP.js';
import '@vitest/snapshot/manager';
import 'node:console';
import 'node:fs';
