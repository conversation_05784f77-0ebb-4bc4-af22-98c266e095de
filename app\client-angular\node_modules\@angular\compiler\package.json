{"name": "@angular/compiler", "version": "20.3.13", "description": "Angular - the compiler library", "author": "angular", "license": "MIT", "engines": {"node": "^20.19.0 || ^22.12.0 || >=24.0.0"}, "dependencies": {"tslib": "^2.3.0"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/compiler"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": ["./fesm2022/compiler.mjs"], "module": "./fesm2022/compiler.mjs", "typings": "./index.d.ts", "type": "module", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/compiler.mjs"}}}