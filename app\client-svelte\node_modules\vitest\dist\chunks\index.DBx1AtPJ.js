import { b as assert, c as createExpect, g as globalExpect, i as inject, s as should, v as vi, d as vitest } from './vi.BiaV1qII.js';
import { b as bench } from './benchmark.B3N2zMcH.js';
import { V as VitestEvaluatedModules } from './evaluatedModules.Dg1zASAC.js';
import { expectTypeOf } from 'expect-type';
import { afterAll, afterEach, beforeAll, beforeEach, describe, it, onTestFailed, onTestFinished, recordArtifact, suite, test } from '@vitest/runner';
import { chai } from '@vitest/expect';

const assertType = function assertType() {};

var index = /*#__PURE__*/Object.freeze({
  __proto__: null,
  EvaluatedModules: VitestEvaluatedModules,
  afterAll: afterAll,
  afterEach: afterEach,
  assert: assert,
  assertType: assertType,
  beforeAll: beforeAll,
  beforeEach: beforeEach,
  bench: bench,
  chai: chai,
  createExpect: createExpect,
  describe: describe,
  expect: globalExpect,
  expectTypeOf: expectTypeOf,
  inject: inject,
  it: it,
  onTestFailed: onTestFailed,
  onTestFinished: onTestFinished,
  recordArtifact: recordArtifact,
  should: should,
  suite: suite,
  test: test,
  vi: vi,
  vitest: vitest
});

export { assertType as a, index as i };
