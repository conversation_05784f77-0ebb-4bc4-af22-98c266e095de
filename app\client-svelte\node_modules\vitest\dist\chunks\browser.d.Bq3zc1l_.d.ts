import { FileSpecification } from '@vitest/runner';
import { T as TestExecutionMethod } from './worker.d.Dxl5oW0C.js';

type SerializedTestSpecification = [project: {
	name: string | undefined;
	root: string;
}, file: string, options: {
	pool: string;
	testLines?: number[] | undefined;
}];

interface BrowserTesterOptions {
	method: TestExecutionMethod;
	files: FileSpecification[];
	providedContext: string;
}

export type { BrowserTesterOptions as B, SerializedTestSpecification as S };
