{"name": "@angular/cli", "version": "20.3.11", "description": "CLI tool for Angular", "main": "lib/cli/index.js", "bin": {"ng": "./bin/ng.js"}, "keywords": ["Angular CLI", "Angular DevKit", "angular", "angular-cli", "devkit", "sdk"], "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli", "dependencies": {"@angular-devkit/architect": "0.2003.11", "@angular-devkit/core": "20.3.11", "@angular-devkit/schematics": "20.3.11", "@inquirer/prompts": "7.8.2", "@listr2/prompt-adapter-inquirer": "3.0.1", "@modelcontextprotocol/sdk": "1.17.3", "@schematics/angular": "20.3.11", "@yarnpkg/lockfile": "1.1.0", "algoliasearch": "5.35.0", "ini": "5.0.0", "jsonc-parser": "3.3.1", "listr2": "9.0.1", "npm-package-arg": "13.0.0", "pacote": "21.0.0", "resolve": "1.22.10", "semver": "7.7.2", "yargs": "18.0.0", "zod": "3.25.76"}, "ng-update": {"migrations": "@schematics/angular/migrations/migration-collection.json", "packageGroup": {"@angular/cli": "20.3.11", "@angular/build": "20.3.11", "@angular/ssr": "20.3.11", "@angular-devkit/architect": "0.2003.11", "@angular-devkit/build-angular": "20.3.11", "@angular-devkit/build-webpack": "0.2003.11", "@angular-devkit/core": "20.3.11", "@angular-devkit/schematics": "20.3.11"}}, "packageManager": "pnpm@10.19.0", "engines": {"node": "^20.19.0 || ^22.12.0 || >=24.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}}