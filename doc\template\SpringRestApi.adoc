= Name of the API

Write a brief summary of the functions of this controller.
Take in account the names of classes and methods.

== Model

Describe state transfer objects used in this controller.
Use the include directive and tags to select only relevant properties with their types and annotations.
Do not repeat properties from the code fragment in the description of the model section.
Let the code be a major part of the documentation.

== Operations

Describe operations of this controller.
Describe collection GET operations with query parameters and pagination by example.
Describe attribute PATCH operation with only one example.
Describe relation PATCH operations individually.

Use only snippets generated by _Spring RESTDocs_ 
Create a curl request for each operation.
Create a sample request for each operation.
Create a sample response for each operation.
Add a warning if snippets are missing.

Describe the response codes for each operation.
