export { l as loadDiffConfig, b as loadSnapshotSerializers, c as setupCommonEnv, s as startCoverageInsideWorker, a as stopCoverageInsideWorker, t as takeCoverageInsideWorker } from './chunks/setup-common.DGHc_BUK.js';
export { collectTests, startTests } from '@vitest/runner';
import * as spyModule from '@vitest/spy';
export { spyModule as SpyModule };
export { format, inspect, stringify } from '@vitest/utils/display';
export { processError } from '@vitest/utils/error';
export { getType } from '@vitest/utils/helpers';
export { DecodedMap, getOriginalPosition } from '@vitest/utils/source-map';
export { getSafeTimers, setSafeTimers } from '@vitest/utils/timers';
import './chunks/coverage.D_JHT54q.js';
import '@vitest/snapshot';
import './chunks/utils.DvEY5TfP.js';

/**
* @internal
*/
const __INTERNAL = { _extendedMethods: /* @__PURE__ */ new Set() };

export { __INTERNAL };
