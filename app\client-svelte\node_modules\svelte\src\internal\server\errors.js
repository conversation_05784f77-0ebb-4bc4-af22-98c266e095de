/* This file is generated by scripts/process-messages/index.js. Do not edit! */

export *  from '../shared/errors.js';

/**
 * Encountered asynchronous work while rendering synchronously.
 * @returns {never}
 */
export function await_invalid() {
	const error = new Error(`await_invalid\nEncountered asynchronous work while rendering synchronously.\nhttps://svelte.dev/e/await_invalid`);

	error.name = 'Svelte error';

	throw error;
}

/**
 * The `html` property of server render results has been deprecated. Use `body` instead.
 * @returns {never}
 */
export function html_deprecated() {
	const error = new Error(`html_deprecated\nThe \`html\` property of server render results has been deprecated. Use \`body\` instead.\nhttps://svelte.dev/e/html_deprecated`);

	error.name = 'Svelte error';

	throw error;
}

/**
 * `%name%(...)` is not available on the server
 * @param {string} name
 * @returns {never}
 */
export function lifecycle_function_unavailable(name) {
	const error = new Error(`lifecycle_function_unavailable\n\`${name}(...)\` is not available on the server\nhttps://svelte.dev/e/lifecycle_function_unavailable`);

	error.name = 'Svelte error';

	throw error;
}